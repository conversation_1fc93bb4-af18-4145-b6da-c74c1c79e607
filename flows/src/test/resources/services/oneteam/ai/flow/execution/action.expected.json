{"primaryIdentifier": "selectForm", "name": "Select form", "description": "", "type": "action", "properties": {"icon": {"name": "search"}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Foundation ID", "type": "variable", "identifier": "foundationId", "properties": {"type": "text", "required": true}}, {"text": "Form configuration ID", "type": "variable", "identifier": "formConfigurationId", "properties": {"type": "select", "required": true, "properties": {"dynamicOptions": {"tag": "formConfigurationId"}}}}, {"text": "Series interval ID", "type": "variable", "identifier": "intervalId", "properties": {"type": "select", "required": false, "defaultValue": "", "properties": {"dynamicOptions": {"tag": "intervalConfigurationId", "body": {"formConfigurationId": "form\"Configuration\"'Id'[0]"}}}}}, {"text": "Form variable name", "type": "variable", "identifier": "formVariableName", "properties": {"type": "text", "required": false, "properties": {"regex": "^[a-zA-Z0-9_]*${'$'}", "defaultValue": "form__step_stepId"}}}, {"text": "Continue flow if form is not found", "type": "select", "identifier": "continueFlowIfNotFound", "properties": {"required": true, "options": [{"value": "true", "label": "Yes"}, {"value": "false", "label": "No"}], "defaultValue": "true"}}], "apiCall": {"url": "/ai/api/forms/select-many", "internal": true, "method": "GET", "body": {"workspaceId": 1, "foundationId": 2, "formConfigurationId": "form\"Configuration\"'Id'[0]", "intervalId": "intervalId", "allowNull": true}, "response": {"type": "json", "properties": {"items": [{"type": "list", "identifier": "forms", "properties": {"items": [{"type": "form.minimal", "identifier": "form"}]}}]}}}, "variableMappings": [{"type": "form.form\"Configuration\"'Id'[0]", "identifier": "formVariableName", "value": 3}]}}}