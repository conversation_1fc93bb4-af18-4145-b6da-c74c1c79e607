{"thisStep": {"foundationConfigurationId": "foundationConfigurationId", "intervalId": "intervalId", "foundationId": 2, "foundationVariableName": "foundationVariableName", "id": "stepId", "continueFlowIfNotFound": true, "response": {"form": {"id": 3}}}, "global": {"workspaceId": 1}, "event": {"eventProperties": {"actor": {"type": "unknown", "id": "unknown"}, "foundation": {"id": 3, "foundationConfiguration": {"id": "foundationConfigurationId"}}}}}