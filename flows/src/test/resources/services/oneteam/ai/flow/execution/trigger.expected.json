{"primaryIdentifier": "foundationCreated", "name": "When a foundation is created", "description": "A trigger that executes when a foundation is created", "type": "trigger", "properties": {"icon": {"name": "roofing"}, "isLocked": true, "isHidden": false, "configuration": {"content": [{"text": "Foundation configuration ID", "type": "select", "identifier": "foundationConfigurationId", "properties": {"required": true, "dynamicOptions": {"tag": "foundationConfigurationId"}}}, {"text": "Foundation variable name", "type": "variable", "identifier": "foundationVariableName", "properties": {"type": "text", "required": false, "properties": {"defaultValue": "foundation", "regex": "^[a-zA-Z0-9_]*$"}}}], "subscribeTo": {"CREATE_collection_foundation": {"key": "CREATE_collection_foundation", "condition": {"lhs": "foundationConfigurationId", "operator": "=", "rhs": "foundationConfigurationId"}, "variableMappings": [{"type": "foundation.foundationConfigurationId", "identifier": "foundationVariableName", "value": 3}]}}}}}