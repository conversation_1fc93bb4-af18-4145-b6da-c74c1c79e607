{"id": "step1", "name": "Condition", "next": "step4", "properties": {"branches": [{"condition": {"AND": [{"lhs": "{{inputValue}}", "operator": ">=", "rhs": "5"}, {"AND": [{"lhs": "{{inputValue}}", "operator": ">", "rhs": "7"}]}]}, "name": "If", "next": "step2"}, {"condition": {"lhs": "{{inputValue}}", "operator": "<", "rhs": "2"}, "name": "If 2", "next": "step3"}, {"condition": {"lhs": "{{answer}}", "operator": "contains", "rhs": "START"}, "name": "If 3", "next": "step3"}]}, "variant": "condition"}