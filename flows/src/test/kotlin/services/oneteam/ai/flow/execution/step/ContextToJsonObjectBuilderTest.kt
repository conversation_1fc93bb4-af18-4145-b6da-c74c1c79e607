package services.oneteam.ai.flow.execution.step

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.flow.execution.DefaultMapBuilder
import services.oneteam.ai.flow.execution.variables.PathBasedVariableFilter
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance

class ContextToJsonObjectBuilderTest {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    @Test
    fun `should build json object from context`() = runTest {
        // Given
        val context = Fixtures.contextWithVars()
        val builder = ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "testCacheKey")

        // When
        val result = builder.build(
            context, PathBasedVariableFilter(setOf("numberVar", "stringVar", "booleanVar", "listVar", "objectVar"))
        )

        // Then
        assertNotNull(result)

        val expected = """
           {"event":{"workspaceId":1,"eventProperties":{"buttonLabel":"string","form":null,"actor":{"type":"flow","flowExecutionId":"flow-execution-id","workspaceId":1,"flowConfigurationId":"flow-id","stepId":"step-id"},"key":"START_flow_manually_from_form"},"id":"1","tenantId":1},"global":{"workspaceId":1,"workspaceVersionId":1,"tenantId":1,"flowConfigurationId":"flow-id","flowConfigurationName":"flow-name","flowDepth":1},"thisStep":{},"_workspace_":{"documentId":"1","id":1,"key":"WORKSPACE1","name":"workspace1","workspaceFoundationId":1,"variables":{}},"numberVar":5,"stringVar":"string literal","booleanVar":true,"listVar":[1,2,3],"objectVar":{"key":"value"}}
        """.trimIndent()

        logger.debug(result.toString())

        assertEquals(Json.decodeFromString<JsonObject>(expected), result)
    }


    @Test
    fun `a number variable should be converted from string to number`() = runTest {
        // Given
        val builder = ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "testCacheKey")
        val context = Fixtures.context()
        context.flowContext.set(
            VariableInstance.Variable(
                JsonPrimitive("5"), VariableDataType.NUMBER, "numberVar"
            )
        )

        // When
        val result = builder.build(context, PathBasedVariableFilter(setOf("numberVar")))

        // Then
        assertNotNull(result)
        assertEquals(JsonPrimitive(5), result["numberVar"])
    }

    @Test
    fun `a boolean variable should be converted from string to boolean`() = runTest {
        // Given
        val builder = ContextToJsonObjectBuilder(listOf(DefaultMapBuilder()), "testCacheKey")
        val context = Fixtures.context()
        context.flowContext.set(
            VariableInstance.Variable(
                JsonPrimitive("true"), VariableDataType.BOOLEAN, "booleanVar"
            )
        )

        // When
        val result = builder.build(context, PathBasedVariableFilter(setOf("booleanVar")))

        // Then
        assertNotNull(result)
        assertEquals(JsonPrimitive(true), result["booleanVar"])
    }

}