package services.oneteam.ai.flow.execution.variables

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonArray
import kotlinx.serialization.json.encodeToJsonElement
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.event.EventActor
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.variables.ListOperation
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.flow.variables.VariableProperties
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion


class ListVariableResolverTest {
    val expressionEvaluator = JsonataExpressionEvaluator()
    val global = FlowContext.GlobalVariables(
        Workspace.Id(1),
        WorkspaceVersion.Id(1),
        1,
        FlowConfiguration.Id("flow-id"),
        FlowConfiguration.Name("flow-name"),
    )

    val workspaceContext = FlowContext.WorkspaceContext(
        documentId = Workspace.DocumentId("1"),
        id = Workspace.Id(1),
        key = Workspace.Key("WORKSPACE1"),
        name = Workspace.Name("workspace1"),
        workspaceFoundationId = Foundation.Id(1),
        variables = emptyMap()
    )

    val event = Event.ForApi(
        Workspace.Id(1), StartFlowManuallyFromFormProperties(
            "string",
            null,
            actor = EventActor.UNKNOWN
        ), Event.Id("1"), 1
    )

    private val listValue = buildJsonArray {
        add(JsonPrimitive(10))
        add(JsonPrimitive(20))
        add(JsonPrimitive(30))
    }

    val context = FlowContextWithLocalStep(
        FlowConfiguration.Step.Id("1"), FlowContext(
            global = global,
            workspace = workspaceContext,
            variables = mutableMapOf(
                "listVariable" to VariableInstance.Variable(
                    Json.encodeToJsonElement(listValue),
                    VariableDataType.LIST,
                    "listVariable",
                    VariableProperties.ListVariableProperties(
                        listOperation = ListOperation.SET_LIST
                    )
                )
            ),
            event = event,
        )
    )

    @Test
    fun `should set list for SET_LIST operation`() = runTest {
        // given
        val newListValue = """
            [40, 50, 60]
        """.trimIndent()
        val variable = VariableInstance.Variable(
            Json.decodeFromString(newListValue),
            VariableDataType.LIST,
            "listVariable",
            VariableProperties.ListVariableProperties(
                listOperation = ListOperation.SET_LIST
            )
        )

        val resolver = ListVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(JsonPrimitive(40))
                add(JsonPrimitive(50))
                add(JsonPrimitive(60))
            }, result.get()
        )
    }

    @Test
    fun `should update item at specific index for SET_ITEM operation`() = runTest {
        // given
        val newValue = 25
        val variable = VariableInstance.Variable(
            Json.encodeToJsonElement(newValue),
            VariableDataType.LIST,
            "listVariable",
            VariableProperties.ListVariableProperties(
                listOperation = ListOperation.SET_ITEM, itemIndex = JsonPrimitive(2)
            )
        )

        val resolver = ListVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(JsonPrimitive(10))
                add(JsonPrimitive(25))
                add(JsonPrimitive(30))
            }, result.get()
        )
    }

    @Test
    fun `should append item to the list for SET_ITEM operation when index is null`() = runTest {
        // given
        val newValue = 40
        val variable = VariableInstance.Variable(
            Json.encodeToJsonElement(newValue),
            VariableDataType.LIST,
            "listVariable",
            VariableProperties.ListVariableProperties(
                listOperation = ListOperation.SET_ITEM
            )
        )

        val resolver = ListVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(JsonPrimitive(10))
                add(JsonPrimitive(20))
                add(JsonPrimitive(30))
                add(JsonPrimitive(40))
            }, result.get()
        )
    }

    @Test
    fun `should remove item at specific index for REMOVE_ITEM operation`() = runTest {
        // given
        val variable = VariableInstance.Variable(
            Json.encodeToJsonElement(""),
            VariableDataType.LIST,
            "listVariable",
            VariableProperties.ListVariableProperties(
                listOperation = ListOperation.REMOVE_ITEM, itemIndex = JsonPrimitive(2)
            )
        )

        val resolver = ListVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(JsonPrimitive(10))
                add(JsonPrimitive(30))
            }, result.get()
        )
    }
}