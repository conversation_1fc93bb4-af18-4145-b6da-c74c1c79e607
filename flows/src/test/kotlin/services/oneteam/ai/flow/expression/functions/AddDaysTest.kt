package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource

class AddDaysTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(AddDays.functionName, AddDays.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `returns date after adding days`() {
        val expression = "\$ADDDAYS(\"2021-01-01\", 1)"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2021-01-02"
    }

    @Test
    fun `returns date after minus days`() {
        val expression = "\$ADDDAYS(\"2021-01-01\", -1)"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2020-12-31"
    }

    @Test
    fun `returns date after adding heaps of days`() {
        val expression = "\$ADDDAYS(\"2021-01-01\", 650)"
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        result shouldBe "2022-10-13"
    }

    @Test
    fun `should error`() {
        val expression = $$"""$ADDDAYS("abc", 650)"""
        val exception = assertThrows<RuntimeException> {
            jsonataWithFunctionRegistered(expression).evaluate(null)
        }
        assertThat(exception.message)
            .contains("Error during function ADDDAYS evaluation: Cannot coerce value to LocalDate: abc")
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            $$"""$ADDDAYS("2020-01-01", 650); 2021-10-12 """,
            $$"""$ADDDAYS("2020-02-29", 365); 2021-02-28 """,
            $$"""$ADDDAYS("2020-02-29", 366); 2021-03-01 """,
            $$"""$ADDDAYS("2020-01-31", 1); 2020-02-01 """,
            $$"""$ADDDAYS("2020-01-31", -1); 2020-01-30 """,
            $$"""$ADDDAYS("2020-03-31", -31); 2020-02-29 """,
            $$"""$ADDDAYS("2019-03-31", -31); 2019-02-28 """,
        ],
        delimiter = ';'
    )
    fun `should add days to date`(expression: String, expected: String) {
        val result = jsonataWithFunctionRegistered(expression).evaluate(null)
        assertThat(result).isEqualTo(expected)
    }


    data class Spec(
        val input: String,
        val expected: String,
    )

    companion object {
        @JvmStatic
        fun validProvider(): List<Spec> {
            return listOf(
                Spec(input = $$"""$ADDDAYS("2020-01-01", 650)""", expected = "2021-10-12")
            )
        }

        @JvmStatic
        fun exceptionProvider(): List<Spec> {
            return listOf(
                Spec(
                    input = $$"""$ADDDAYS("abc", 650)""",
                    expected = "Error during function ADDDAYS evaluation: Cannot coerce value to LocalDate: abc"
                ),
                Spec(
                    input = $$"""$ADDDAYS("2020-01-01", "abc")""",
                    expected = """Error during function ADDDAYS evaluation: Character a is neither a decimal digit number, decimal point, nor "e" notation exponential mark."""
                ),
            )
        }
    }

    @ParameterizedTest
    @MethodSource("validProvider")
    fun `should add days`(spec: Spec) = runTest {
        val result = jsonataWithFunctionRegistered(spec.input).evaluate(null)
        assertThat(result).isEqualTo(spec.expected)
    }

    @ParameterizedTest
    @MethodSource("exceptionProvider")
    fun `should throw error when adding days`(spec: Spec) = runTest {
        val exception = assertThrows<Exception> {
            jsonataWithFunctionRegistered(spec.input).evaluate(null)
        }
        assertThat(exception.message).contains(spec.expected)
    }
}