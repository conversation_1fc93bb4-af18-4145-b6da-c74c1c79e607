package services.oneteam.ai.flow.execution.step

import io.kotest.matchers.string.shouldContain
import io.ktor.client.engine.mock.*
import io.ktor.client.request.forms.*
import io.ktor.http.*
import io.ktor.http.content.*
import io.ktor.utils.io.*
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withContext
import kotlinx.io.readByteArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonObject
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import services.oneteam.ai.flow.execution.FlowExecution.Step
import services.oneteam.ai.flow.execution.listeners.FlowListenerLogger
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.common.OTAIHeaders
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.event.EventActor
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.proxy.ExternalProxyService
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.otSerializer
import kotlin.test.assertTrue

class ActionExecutionStepContentTypeTest {

    /**
     * Test that ActionExecutionStep sends API requests with correct content type and body format
     * for different content types: JSON, FormUrlEncoded, Multipart
     * This ensures same behaviour for API calls to external systems [ExternalProxyService]
     * Using [ActionExecutionStepPopulateApiCallTest] as reference
     */
    @ParameterizedTest
    @MethodSource("apiCallTestCases")
    fun `action step should call http builder with correct content type and body`(spec: Spec) = runTest {
        // PREPARE
        val filePressService = mockk<FilePressService>()
        // Mock filePressService to return test file content when requested
        every { filePressService.getFileBytes(MultipartTestConstants.FILE_PATH) } returns MultipartTestConstants.FILE_CONTENT.toByteArray()

        // Create a proxy service with a mock client to intercept and verify requests
        // Assertions are done in the mock client handler
        // externalProxyWithMockClient uses [MockEngine] under the hood
        val proxyService: ExternalProxyService = ProxyFixtures.externalProxyWithMockClient { request ->
            // Assert request properties
            request.url.encodedPath shouldContain spec.url
            request.body.contentType.toString() shouldContain spec.contentTypeStringToAssert
            val body = request.body
            spec.bodyAssertion(body)
            body.headers.contains(OTAIHeaders.X_OTAI_FLOW)
            // Return mock response
            respondOk("{}")
        }

        val flowStepTypeConfiguration = FlowStepTypeConfiguration(
            1, "myAction", "action", "My Action", null, FlowStepType.Properties(
                icon = FlowStepType.Properties.Icon("search"),
                isLocked = true,
                configuration = FlowStepType.Properties.Configuration(
                    apiCall = FlowStepType.Properties.Configuration.ApiCall(
                        url = spec.url,
                        body = spec.bodyJson,
                        headers = spec.headers,
                        contentType = spec.contentTypeStringToSend,
                        method = "POST",
                        internal = false,
                        response = FlowStepType.Properties.Configuration.ApiCall.Response(
                            type = "json", properties = FlowStepType.Properties.Configuration.ApiCall.Response.Property(
                                items = listOf()
                            )
                        )
                    ),
                    variableMappings = listOf(),
                ),
            )
        )

        val context = Fixtures.context()
        context.flowContext.listeners.add(FlowListenerLogger())

        val mapBuilders = Fixtures.Context.buildMapBuilders()

        // since there's nothing to populate in the body, we can just use the body as is
        val apiCallJson = otSerializer.encodeToJsonElement(
            FlowStepType.Properties.Configuration.ApiCall.serializer(),
            flowStepTypeConfiguration.properties!!.configuration!!.apiCall!!
        ) as JsonObject

        val actionStep = ActionExecutionStep(
            Step(
                id = FlowConfiguration.Step.Id("1"),
                name = "My Action",
                variant = Step.Variant.ACTION,
                properties = Step.Properties(
                    typePrimaryIdentifier = "myAction", variables = mutableListOf(), apiCall = apiCallJson
                ),
                next = FlowConfiguration.Step.Id("2")
            ),
            flowStepTypeConfiguration,
            proxyService = proxyService,
            internalProxyService = proxyService,
            filePressService = filePressService,
            contextToJsonObjectBuilder = ContextToJsonObjectBuilder(
                mapBuilders, FlowConfiguration.Step.Id("1").value
            ),
            expressionEvaluator = JsonataExpressionEvaluator()
        )

        val testTenant = Tenant(
            id = 1,
            originUrl = "http://localhost",
            name = "Test Tenant",
            internalUrl = "http://localhost",
            internalSyncUrl = "http://localhost",
        )

        // PERFORM
        withContext(
            RequestContext(
                testTenant,
                actor = EventActor.Flow(Workspace.Id(1), FlowConfiguration.Id("fc_123"), "fe_123")
            )
        ) {
            actionStep.execute(context)
        }

        // No need for coVerify, assertions are in the mock engine
    }

    private object MultipartTestConstants {
        const val FILENAME = "test.txt"
        const val FILE_FIELD_NAME = "file1"
        const val FILE_PATH = "/path/to/$FILENAME"
        const val FILE_CONTENT = "test file content"
    }

    companion object {
        data class Spec(
            val description: String,
            val url: String,
            val contentTypeStringToAssert: String,
            val contentTypeStringToSend: String = contentTypeStringToAssert,
            val bodyJson: JsonObject,
            val bodyAssertion: (OutgoingContent) -> Unit,
            val headers: JsonElement? = null
        )

        @JvmStatic
        fun apiCallTestCases(): List<Spec> = listOf(
            Spec(
                description = "Json Content Type should send TextContent body",
                url = "/test-json-content-type",
                contentTypeStringToAssert = ContentType.Application.Json.toString(),
                bodyJson = JsonObject(mapOf("test" to JsonPrimitive("123"))),
                bodyAssertion = { body ->
                    // assert type of body should be TextContent
                    assertTrue("Body should be TextContent for JSON content type") { body is TextContent }
                    (body as TextContent).text shouldContain "\"test\":\"123\""
                }
            ),
            Spec(
                description = "FormUrlEncoded Content Type should send FormDataContent body",
                url = "/test-form-content-type",
                contentTypeStringToAssert = ContentType.Application.FormUrlEncoded.toString(),
                bodyJson = JsonObject(mapOf("test" to JsonPrimitive("123"))),
                bodyAssertion = { body ->
                    // assert type of body should be FormDataContent
                    assertTrue("Body should be FormDataContent for JSON content type") { body is FormDataContent }
                    (body as FormDataContent).bytes().toString(Charsets.UTF_8) shouldContain "test=123"
                }
            ),
            Spec(
                description = "Multipart Content Type should send MultiPartFormDataContent body with file",
                url = "/test-multipart-content-type",
                contentTypeStringToAssert = ContentType.MultiPart.FormData.toString(),
                bodyJson = buildJsonObject {
                    put("files", buildJsonObject {
                        put(MultipartTestConstants.FILE_FIELD_NAME, buildJsonObject {
                            put("name", JsonPrimitive(MultipartTestConstants.FILENAME))
                            put("path", JsonPrimitive(MultipartTestConstants.FILE_PATH))
                        })
                    })
                },
                bodyAssertion = { body ->
                    assertTrue("Body should be MultiPartFormDataContent for multipart content type") { body is MultiPartFormDataContent }
                    val multipart = body as MultiPartFormDataContent
                    // Serialize to bytes and check for expected content
                    val channel = ByteChannel()
                    runBlocking {
                        multipart.writeTo(channel)
                        val multipartBytes = channel.readRemaining().readByteArray()
                        val multipartString = multipartBytes.toString(Charsets.UTF_8)
                        multipartString shouldContain multipart.boundary
                        multipartString shouldContain "Content-Disposition: form-data"
                        multipartString shouldContain "filename=\"${MultipartTestConstants.FILENAME}\""
                        multipartString shouldContain MultipartTestConstants.FILE_CONTENT
                    }
                }
            ),
            Spec(
                description = "Empty Content Type should default to JSON and send TextContent body",
                url = "/test-json-content-type",
                contentTypeStringToAssert = ContentType.Application.Json.toString(),
                contentTypeStringToSend = "",
                bodyJson = JsonObject(mapOf("test" to JsonPrimitive("123"))),
                bodyAssertion = { body ->
                    // assert type of body should be TextContent
                    assertTrue("Body should be TextContent for JSON content type") { body is TextContent }
                    (body as TextContent).text shouldContain "\"test\":\"123\""
                }
            ),
            Spec(
                description = "Wrong Content Type in Headers should be ignored and default to JSON and send TextContent body",
                url = "/test-json-content-type",
                contentTypeStringToAssert = ContentType.Application.Json.toString(),
                contentTypeStringToSend = "",
                headers = JsonObject(mapOf("Content-Type" to JsonPrimitive(ContentType.MultiPart.FormData.toString()))),
                bodyJson = JsonObject(mapOf("test" to JsonPrimitive("123"))),
                bodyAssertion = { body ->
                    // assert type of body should be TextContent
                    assertTrue("Body should be TextContent for JSON content type") { body is TextContent }
                    (body as TextContent).text shouldContain "\"test\":\"123\""
                }
            )
        )
    }
}
