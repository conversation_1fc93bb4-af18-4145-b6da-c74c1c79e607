package services.oneteam.ai.flow.spike

import com.dashjoin.jsonata.JException
import com.dashjoin.jsonata.Jsonata
import kotlinx.serialization.json.*
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Timeout
import org.junit.jupiter.api.assertThrows
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.util.concurrent.TimeUnit

class JsonataSpike {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun JsonElement.toSimpleValue(): Any? {
        return when (this) {
            is JsonObject -> this.mapValues { it.value.toSimpleValue() }
            is JsonArray -> this.map { it.toSimpleValue() }
            is JsonPrimitive -> when {
                this.isString -> this.content
                this.booleanOrNull != null -> this.boolean
                this.intOrNull != null -> this.int
                this.doubleOrNull != null -> this.double
                else -> null
            }

            JsonNull -> null
        }
    }


    @Test
    fun `should evaluate`() {
        val expression = """
            ("ahFVQ2Mk6tZ0r1DKHICTG" = "ahFVQ2Mk6tZ0r1DKHICTG"
            and (
              ("flow" in ["flow", "user"]) = false 
              or ("flow" = flow and "flow" = flow) 
              or ("flow" = user and "flow" = user)
             )
            )
        """.trimIndent()
        val jsonata = Jsonata.jsonata(expression)
        val result = jsonata.evaluate(mapOf<Any, Any>())
        assertThat(result).isEqualTo(false)
    }

    @Test
    fun `should evaluate - with quotes`() {
        val expression = """
            ("ahFVQ2Mk6tZ0r1DKHICTG" = "ahFVQ2Mk6tZ0r1DKHICTG"
            and (
              ("flow" in ["flow", "user"]) = false 
              or ("flow" = "flow" and "flow" = "flow") 
              or ("flow" = "user" and "flow" = "user")
             )
            )
        """.trimIndent()
        val jsonata = Jsonata.jsonata(expression)
        val result = jsonata.evaluate(mapOf<Any, Any>())
        println(result)
        assertThat(result).isEqualTo(true)
    }


    @Test
    fun `should do maths`() {
        val expression = "0.8 - 0.1"
        val jsonata = Jsonata.jsonata(expression)
        val result = jsonata.evaluate(mapOf<Any, Any>())
        assertThat(result).isEqualTo(0.7000000000000001)
    }

    @Test
    fun `should do maths with big decimal`() {
        val expression = "a - b"
        val jsonata = Jsonata.jsonata(expression)
        val result = jsonata.evaluate(
            mapOf<Any, Any>(
                "a" to BigDecimal("0.8"),
                "b" to BigDecimal("0.1")
            )
        )
        assertThat(result).isEqualTo(0.7000000000000001)
    }

    @Test
    fun `should return null when no data`() {
        val expression = "a.b.c"
        val jsonata = Jsonata.jsonata(expression)
        val result = jsonata.evaluate(mapOf<Any, Any>())
        assertThat(result).isNull()
    }

    @Test
    fun `should throw exception for invalid expression`() {
        val expression = "\$sum(null, null)"
        val jsonata = Jsonata.jsonata(expression)
        assertThrows<JException> {
            jsonata.evaluate(mapOf<Any, Any>())
        }
    }

    @Test
    fun `should not throw exception when counting nulls`() {
        val expression = "\$count([null, null])"
        val jsonata = Jsonata.jsonata(expression)
        val result = jsonata.evaluate(mapOf<Any, Any>())
        assertThat(result).isEqualTo(2)
    }

    @Test
    fun `should work with normal map`() {
        val expression = "a.b.c"
        val jsonata = Jsonata.jsonata(expression)
        val result = jsonata.evaluate(mapOf<Any, Any>("a" to mapOf("b" to mapOf("c" to "value"))))
        assertThat(result).isEqualTo("value")
    }

    @Test
    fun `should work if json object converted to simple types`() {

        val context = Json.decodeFromString<JsonObject>(
            """
                { 
                    "a": {
                       "b": {
                          "c": "value"
                       }
                    }
                }
            """.trimIndent()
        )
        val jsonata = Jsonata.jsonata("a.b.c")
        val result = jsonata.evaluate(context.toSimpleValue())
        logger.debug("Result {}", result)

        assertThat(result).isEqualTo("value")
    }

    // java.lang.IllegalArgumentException: Only JSON types (values, Map, List) are allowed as input. Unsupported type: kotlinx.serialization.json.JsonLiteral
    @Test
    fun `should not work with json object`() {
        assertThrows<IllegalArgumentException> {
            val context = Json.decodeFromString<JsonObject>(
                """
                { 
                    "a": {
                       "b": {
                          "c": "value"
                       }
                    }
                }
            """.trimIndent()
            )
            val jsonata = Jsonata.jsonata("a.b.c")
            // throws exception
            jsonata.evaluate(context)
        }
    }

    // java.lang.IllegalArgumentException: Only JSON types (values, Map, List) are allowed as input. Unsupported type: kotlinx.serialization.json.JsonLiteral
    @Disabled
    @Test
    fun `should filter json object`() {
        val context = Json.decodeFromString<JsonObject>(
            """
                { "data":  
                    [
                      {
                        "bihoQERn53": "tofilter",
                        "Psnak4wzY6": 1,
                        "_rowId": "06Mo6fCQ490_Y3HHVEWHG",
                        "_rowIndex": 1
                      },
                      {
                        "bihoQERn53": "good value",
                        "Psnak4wzY6": 4,
                        "_rowId": "vo8h74_wwrGf2Fg7jmHvB",
                        "_rowIndex": 2
                      },
                      {
                        "bihoQERn53": "good value 2",
                        "Psnak4wzY6": 6,
                        "_rowId": "IYC7PUjhfhUJ8kVesGXAb",
                        "_rowIndex": 3
                      }
                    ]
                }
            """.trimIndent()
        )
        val jsonata = Jsonata.jsonata("data[Psnak4wzY6 > 3 and \$power(Psnak4wzY6, 2) < 30]")
        val result = jsonata.evaluate(context)
        logger.debug("Result {}", result)

        assertThat(result).isNotNull()
    }

    @Test
    // this timeout means if the test is failing then it will fail quickly
    @Timeout(
        value = 10,
        unit = TimeUnit.SECONDS,
        threadMode = Timeout.ThreadMode.SEPARATE_THREAD
    )
    fun `infinite loop should time out`() {
        // https://docs.jsonata.org/programming#recursive-functions
        val expression = """
            (
              ${'$'}func := function(${'$'}x) {
                (${'$'}x < 0) ?
                  ${'$'}func(${'$'}x) /* This will cause an infinite loop for negative numbers */
                : (${'$'}x = 0) ? "Zero" : "Positive"

              };
              ${'$'}result := ${'$'}map([1, -1, 0, 2], ${'$'}func)
            )
        """.trimIndent()

        val frame = Jsonata.Frame(null)
        frame.setRuntimeBounds(1000, 50)

        // Code should cause an infinite loop
        // but the frame should make it timeout with
        //        JSonataException Expression evaluation timeout: Check for infinite loop
        //            com.dashjoin.jsonata.JException: JSonataException Expression evaluation timeout: Check for infinite loop
        val jsonata = Jsonata.jsonata(expression)
        val context = mapOf<Any, Any>()

        assertThatThrownBy {
            jsonata.evaluate(context, frame)
        }.hasMessageContaining("JSonataException Expression evaluation timeout: Check for infinite loop")
    }

    @Test
    fun `should filter by type`() {
        val context = Json.decodeFromString<JsonObject>(
            """
            {
              "Phone": [
                {
                  "type": "home",
                  "number": "0203 544 1234"
                },
                {
                  "type": "office",
                  "number": "01962 001234"
                },
                {
                  "type": "office",
                  "number": "01962 001235"
                },
                {
                  "type": "mobile",
                  "number": "077 7700 1234"
                }
              ]
            }
            """.trimIndent()
        )
        val jsonata = Jsonata.jsonata("""Phone[type = "mobile"].number""")
        val result = jsonata.evaluate(context.toSimpleValue())
        assertThat(result).isNotNull()
        assertThat(result).isEqualTo("077 7700 1234")
    }

    @Test
    fun `should filter by type with dynamic selector`() {
        val context = Json.decodeFromString<JsonObject>(
            """
            {
              "typeToFind": "mobile",
              "Phone": [
                {
                  "type": "home",
                  "number": "0203 544 1234"
                },
                {
                  "type": "office",
                  "number": "01962 001234"
                },
                {
                  "type": "office",
                  "number": "01962 001235"
                },
                {
                  "type": "mobile",
                  "number": "077 7700 1234"
                }
              ]
            }
            """.trimIndent()
        )
        val jsonata = Jsonata.jsonata("Phone[type = $$.typeToFind].number")
        val result = jsonata.evaluate(context.toSimpleValue())
        assertThat(result).isNotNull()
        assertThat(result).isEqualTo("077 7700 1234")
    }
}