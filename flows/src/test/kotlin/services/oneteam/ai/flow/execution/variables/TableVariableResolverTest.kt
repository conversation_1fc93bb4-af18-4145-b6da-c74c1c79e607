package services.oneteam.ai.flow.execution.variables

import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.flow.execution.FlowContextWithLocalStep
import services.oneteam.ai.flow.expression.JsonataExpressionEvaluator
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.event.EventActor
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.variables.TableOperation
import services.oneteam.ai.shared.domains.flow.variables.VariableDefinition
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.flow.variables.VariableProperties
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion

class TableVariableResolverTest {
    val expressionEvaluator = JsonataExpressionEvaluator()
    val global = FlowContext.GlobalVariables(
        Workspace.Id(1),
        WorkspaceVersion.Id(1),
        1,
        FlowConfiguration.Id("flow-id"),
        FlowConfiguration.Name("flow-name"),
    )

    val workspaceContext = FlowContext.WorkspaceContext(
        documentId = Workspace.DocumentId("1"),
        id = Workspace.Id(1),
        key = Workspace.Key("WORKSPACE1"),
        name = Workspace.Name("workspace1"),
        workspaceFoundationId = Foundation.Id(1),
        variables = emptyMap()
    )

    val event = Event.ForApi(
        Workspace.Id(1), StartFlowManuallyFromFormProperties(
            "string",
            null,
            actor = EventActor.UNKNOWN
        ), Event.Id("1"), 1
    )

    val tableValue = buildJsonArray {
        add(buildJsonObject {
            put("rowId", JsonPrimitive("a1"))
            put("name", JsonPrimitive("John"))
            put("age", JsonPrimitive(30))
        })
        add(buildJsonObject {
            put("rowId", JsonPrimitive("a2"))
            put("name", JsonPrimitive("Jane"))
            put("age", JsonPrimitive(25))
        })
    }
    val context = FlowContextWithLocalStep(
        FlowConfiguration.Step.Id("1"), FlowContext(
            global = global,
            workspace = workspaceContext,
            variables = mutableMapOf(
                "tableVariable" to VariableInstance.Variable(
                    Json.encodeToJsonElement(tableValue),
                    VariableDataType.fromString("table"),
                    "tableVariable",
                    VariableProperties.TableVariableProperties(
                        operation = TableOperation.SET_TABLE, columns = listOf(
                            VariableDefinition.VariableConfiguration(
                                "rowId",
                                VariableDataType.fromString("text"),
                                "rowId"
                            ),
                            VariableDefinition.VariableConfiguration(
                                "name",
                                VariableDataType.fromString("text"),
                                "name"
                            ),
                            VariableDefinition.VariableConfiguration(
                                "age",
                                VariableDataType.fromString("number"),
                                "age"
                            )
                        )
                    )
                ),
                "testArray" to VariableInstance.Variable(
                    buildJsonArray { add(JsonPrimitive(89)) }, VariableDataType.fromString("array"), "testArray"
                ),
            ),
            event = event,
        )
    )

    @Test
    fun `should resolve setTable`() = runTest {
        // given
        val tableValue = """
            [
                {
                    "name": "Lucas",
                    "age": 30
                },
                {
                    "name": "Jenifer",
                    "age": 20
                }
            ]
        """.trimIndent()

        val variable = VariableInstance.Variable(
            Json.decodeFromString(tableValue),
            VariableDataType.fromString("table"),
            "tableVariable",
            VariableProperties.TableVariableProperties(
                operation = TableOperation.SET_TABLE, columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", VariableDataType.fromString("text"), "rowId"),
                    VariableDefinition.VariableConfiguration("name", VariableDataType.fromString("text"), "name"),
                    VariableDefinition.VariableConfiguration("age", VariableDataType.fromString("number"), "age")
                )
            )
        )

        val resolver = TableVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("name", JsonPrimitive("Lucas"))
                    put("age", JsonPrimitive(30))
                })
                add(buildJsonObject {
                    put("name", JsonPrimitive("Jenifer"))
                    put("age", JsonPrimitive(20))
                })
            }, result.get()
        )
    }

    @Test
    fun `should add row to the existing table for setRow with empty rowId and rowIndex`() = runTest {
        // given
        val tableValue = """
            {
                "rowId": "a3",
                "name": "Luke",
                "age": 30
            }
        """.trimIndent()
        val variable = VariableInstance.Variable(
            Json.decodeFromString(tableValue),
            VariableDataType.fromString("table"),
            "tableVariable",
            VariableProperties.TableVariableProperties(
                operation = TableOperation.SET_ROW, columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", VariableDataType.fromString("text"), "rowId"),
                    VariableDefinition.VariableConfiguration("name", VariableDataType.fromString("text"), "name"),
                    VariableDefinition.VariableConfiguration("age", VariableDataType.fromString("number"), "age")
                )
            )
        )

        val resolver = TableVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(30))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonPrimitive(25))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a3"))
                    put("name", JsonPrimitive("Luke"))
                    put("age", JsonPrimitive(30))
                })
            }, result.get()
        )
    }

    @Test
    fun `should replace row in the existing table for setRow with rowIndex`() = runTest {
        // given
        val tableValue = """
            {
                "rowId": "a2",
                "name": "Luke",
                "age": 30
            }
        """.trimIndent()
        val variable = VariableInstance.Variable(
            Json.decodeFromString(tableValue),
            VariableDataType.TABLE,
            "tableVariable",
            VariableProperties.TableVariableProperties(
                operation = TableOperation.SET_ROW, rowIndex = JsonPrimitive(2), columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", VariableDataType.TEXT, "rowId"),
                    VariableDefinition.VariableConfiguration("name", VariableDataType.TEXT, "name"),
                    VariableDefinition.VariableConfiguration("age", VariableDataType.NUMBER, "age")
                )
            )
        )

        val resolver = TableVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(30))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Luke"))
                    put("age", JsonPrimitive(30))
                })
            }, result.get()
        )
    }

    @Test
    fun `should replace row in the existing table for setRow with rowId`() = runTest {
        // given
        val tableValue = """
            {
                "rowId": "a2",
                "name": "Luke",
                "age": 30
            }
        """.trimIndent()
        val variable = VariableInstance.Variable(
            Json.decodeFromString(tableValue),
            VariableDataType.TABLE,
            "tableVariable",
            VariableProperties.TableVariableProperties(
                operation = TableOperation.SET_ROW, rowIdentifier = "a2", columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", VariableDataType.TEXT, "rowId"),
                    VariableDefinition.VariableConfiguration("name", VariableDataType.TEXT, "name"),
                    VariableDefinition.VariableConfiguration("age", VariableDataType.NUMBER, "age")
                )
            )
        )

        val resolver = TableVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(30))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Luke"))
                    put("age", JsonPrimitive(30))
                })
            }, result.get()
        )
    }

    @Test
    fun `should replace cell in the existing table for setCell with rowIndex`() = runTest {
        // given
        val tableValue = "100"
        val variable = VariableInstance.Variable(
            Json.decodeFromString(tableValue),
            VariableDataType.TABLE,
            "tableVariable",
            VariableProperties.TableVariableProperties(
                operation = TableOperation.SET_CELL,
                rowIndex = JsonPrimitive(2),
                columnIdentifier = "age",
                columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", VariableDataType.TEXT, "rowId"),
                    VariableDefinition.VariableConfiguration("name", VariableDataType.TEXT, "name"),
                    VariableDefinition.VariableConfiguration("age", VariableDataType.NUMBER, "age")
                )
            )
        )

        val resolver = TableVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(30))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonPrimitive(100))
                })
            }, result.get()
        )
    }

    @Test
    fun `should replace cell in the existing table for setCell with rowIdentifier`() = runTest {
        // given
        val tableValue = "100"
        val variable = VariableInstance.Variable(
            Json.decodeFromString(tableValue),
            VariableDataType.TABLE,
            "tableVariable",
            VariableProperties.TableVariableProperties(
                operation = TableOperation.SET_CELL, rowIdentifier = "a2", columnIdentifier = "age", columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", VariableDataType.TEXT, "rowId"),
                    VariableDefinition.VariableConfiguration("name", VariableDataType.TEXT, "name"),
                    VariableDefinition.VariableConfiguration("age", VariableDataType.NUMBER, "age")
                )
            )
        )

        val resolver = TableVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(30))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonPrimitive(100))
                })
            }, result.get()
        )
    }

    @Test
    fun `should replace column for setColumn`() = runTest {
        // given
        val tableValue = """
            [ 89, 90 ]
        """.trimIndent()
        val variable = VariableInstance.Variable(
            Json.decodeFromString(tableValue),
            VariableDataType.TABLE,
            "tableVariable",
            VariableProperties.TableVariableProperties(
                operation = TableOperation.SET_COLUMN, columnIdentifier = "age", columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", VariableDataType.TEXT, "rowId"),
                    VariableDefinition.VariableConfiguration("name", VariableDataType.TEXT, "name"),
                    VariableDefinition.VariableConfiguration("age", VariableDataType.NUMBER, "age")
                )
            )
        )

        val resolver = TableVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(89))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonPrimitive(90))
                })
            }, result.get()
        )
    }

    @Test
    fun `setColumn - column size is longer than table row size`() = runTest {
        // given
        val tableValue = """
            [ 89, 90, 91 ]
        """.trimIndent()
        val variable = VariableInstance.Variable(
            Json.decodeFromString(tableValue),
            VariableDataType.TABLE,
            "tableVariable",
            VariableProperties.TableVariableProperties(
                operation = TableOperation.SET_COLUMN, columnIdentifier = "age", columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", VariableDataType.TEXT, "rowId"),
                    VariableDefinition.VariableConfiguration("name", VariableDataType.TEXT, "name"),
                    VariableDefinition.VariableConfiguration("age", VariableDataType.NUMBER, "age")
                )
            )
        )

        val resolver = TableVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(89))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonPrimitive(90))
                })
                add(buildJsonObject {
                    put("age", JsonPrimitive(91))
                })
            }, result.get()
        )
    }

    @Test
    fun `setColumn - column size is shorter than table row size`() = runTest {
        // given
        val tableValue = """
            [ 89 ]
        """.trimIndent()
        val variable = VariableInstance.Variable(
            Json.decodeFromString(tableValue),
            VariableDataType.TABLE,
            "tableVariable",
            VariableProperties.TableVariableProperties(
                operation = TableOperation.SET_COLUMN, columnIdentifier = "age", columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", VariableDataType.TEXT, "rowId"),
                    VariableDefinition.VariableConfiguration("name", VariableDataType.TEXT, "name"),
                    VariableDefinition.VariableConfiguration("age", VariableDataType.NUMBER, "age")
                )
            )
        )

        val resolver = TableVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(89))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonNull)
                })
            }, result.get()
        )
    }

    @Test
    fun `should resolve when value is JsonArray`() = runTest {
        // given
        val tableValue = """
            {{testArray}}
        """.trimIndent()
        val variable = VariableInstance.Variable(
            buildJsonArray { add(JsonPrimitive(89)) },
            VariableDataType.TABLE,
            "tableVariable",
            VariableProperties.TableVariableProperties(
                operation = TableOperation.SET_COLUMN, columnIdentifier = "age", columns = listOf(
                    VariableDefinition.VariableConfiguration("rowId", VariableDataType.TEXT, "rowId"),
                    VariableDefinition.VariableConfiguration("name", VariableDataType.TEXT, "name"),
                    VariableDefinition.VariableConfiguration("age", VariableDataType.TEXT, "age")
                )
            )
        )

        val resolver = TableVariableOperation(true, expressionEvaluator)

        // when
        val result = resolver.resolve(variable, context)

        // then
        assertEquals(
            buildJsonArray {
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a1"))
                    put("name", JsonPrimitive("John"))
                    put("age", JsonPrimitive(89))
                })
                add(buildJsonObject {
                    put("rowId", JsonPrimitive("a2"))
                    put("name", JsonPrimitive("Jane"))
                    put("age", JsonNull)
                })
            }, result.get()
        )
    }
}