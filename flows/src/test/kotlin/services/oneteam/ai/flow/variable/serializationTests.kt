package services.oneteam.ai.flow.variable

import kotlinx.serialization.json.JsonPrimitive
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import services.oneteam.ai.flow.execution.FlowContext
import services.oneteam.ai.shared.domains.VariableDataType
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.Event.EventProperties.StartFlowManuallyFromFormProperties
import services.oneteam.ai.shared.domains.event.EventActor
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance
import services.oneteam.ai.shared.domains.flow.variables.VariableInstance.SecuredVariable.Companion.SECURED_PREFIX
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.otSerializer

class SerializationTests {

    val global = FlowContext.GlobalVariables(
        workspaceId = Workspace.Id(1),
        workspaceVersionId = WorkspaceVersion.Id(1),
        tenantId = 1,
        flowConfigurationId = FlowConfiguration.Id("1"),
        flowConfigurationName = FlowConfiguration.Name("flow")
    )

    val event = Event.ForApi(
        Workspace.Id(1), StartFlowManuallyFromFormProperties(
            buttonLabel = "string",
            form = null,
            actor = EventActor.UNKNOWN
        ), Event.Id("1"), 1
    )

    @Test
    fun `should serialize variable`() {
        val variable = VariableInstance.Variable(
            JsonPrimitive("value"), VariableDataType.TEXT, "key", null
        )

        val serialized = otSerializer.encodeToString(variable)

        val deserialized = otSerializer.decodeFromString<VariableInstance>(serialized)

        assert(deserialized.get() == variable.get())
    }

    @Test
    fun `should serialize secured variable`() {

        val variable = VariableInstance.SecuredVariable(
            JsonPrimitive("value"), VariableDataType.TEXT, "key", null, "123"
        )

        val serialized = otSerializer.encodeToString(variable)

        val deserialized = otSerializer.decodeFromString<VariableInstance.SecuredVariable>(serialized)

        assert(deserialized.get() == variable.get())
    }

    @Test
    fun `should deserialize variable`() {
        val variable = VariableInstance.Variable(
            JsonPrimitive("value"), VariableDataType.TEXT, "key", null
        )

        val serialized = otSerializer.encodeToString(variable)

        val deserialized = otSerializer.decodeFromString<VariableInstance>(serialized)

        assert(deserialized.get() == variable.get())
    }

    @Test
    fun `should deserialize secured variable`() {
        val variable = VariableInstance.SecuredVariable(
            JsonPrimitive("value"), VariableDataType.TEXT, "key", null, "123"
        )

        val serialized = otSerializer.encodeToString(variable)

        val deserialized = otSerializer.decodeFromString<VariableInstance.SecuredVariable>(serialized)

        assert(deserialized.get() == variable.get())
    }

    @Test
    fun `serialized securedVariables should not contain masked values`() {

        val securedVariable = VariableInstance.SecuredVariable(
            value = JsonPrimitive("maskedValue"),
            type = VariableDataType.TEXT,
            identifier = "securedTest",
            securedRef = "123"
        )

        val out = otSerializer.encodeToString(securedVariable)

        assertThat(out).doesNotContain("maskedValue")
    }

    @Test
    fun `deserialized securedVariables should serialize to default unsecured variable`() {

        val jsonText = """
        {
            "type": "text",
            "value": "hello",
            "identifier": "goodbye"
        }
            """.trimIndent()

        val out = otSerializer.decodeFromString<VariableInstance>(jsonText)

        assertThat(out).isInstanceOf(VariableInstance.Variable::class.java)

        assert(out.get() == JsonPrimitive("hello"))
    }

    @Test
    fun `json with discriminator deserializes to correct type`() {
        val jsonText = """
        {
            "polyType": "SECURED_VARIABLE",
            "type": "text",
            "value": "hello",
            "identifier": "goodbye",
            "securedRef": "123"
        }
            """.trimIndent()

        val out = otSerializer.decodeFromString<VariableInstance>(jsonText)

        assertThat(out).isInstanceOf(VariableInstance.SecuredVariable::class.java)

        assert(out.get() == JsonPrimitive(SECURED_PREFIX + "123"))
    }

    @Test
    fun `secured variable value should serialize to secured ref`() {
        val securedVariable = VariableInstance.SecuredVariable(
            value = JsonPrimitive("maskedValue"),
            type = VariableDataType.TEXT,
            identifier = "securedTest",
            securedRef = "123"
        )

        val serialized = otSerializer.encodeToString(securedVariable)

        assertThat(serialized).contains("\"securedRef\":\"123\"")
        assertThat(serialized).doesNotContain("maskedValue")
    }
}