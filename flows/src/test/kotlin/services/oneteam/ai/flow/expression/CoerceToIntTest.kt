package services.oneteam.ai.flow.expression

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class CoerceToIntTest {

    @Test
    fun `should coerce string int to int`() {
        val result = coerceToInt("123")
        assertThat(result).isEqualTo(123)
    }

    @Test
    fun `should coerce string float to int`() {
        assertThat(coerceToInt("7.0")).isEqualTo(7)
        assertThat(coerceToInt("7.5")).isEqualTo(7)
        assertThat(coerceToInt("7.9")).isEqualTo(7)
    }

}