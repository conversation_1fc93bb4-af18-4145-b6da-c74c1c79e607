package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test

class LenTest {
    fun jsonataWithFunctionRegistered(expression: String): Jsonata {
        val jsonataExpression = Jsonata.jsonata(expression)
        jsonataExpression.registerFunction(Len.functionName, Len.evaluatorFunction)
        return jsonataExpression
    }

    @Test
    fun `should return the length of a string`() {
        val expression = "\$LEN('Hello, world!')"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 13
    }

    @Test
    fun `should return 0 when the string is empty`() {
        val expression = "\$LEN('')"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(null)
        result shouldBe 0
    }

    @Test
    fun `should return the length of a context value`() {
        val expression = $$"$LEN(input1)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(mapOf("input1" to "Hello, world!"))
        result shouldBe 13
    }

    @Test
    fun `should return 0 for null input`() {
        val expression = $$"$LEN(input1)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        val result = jsonata.evaluate(mapOf("input1" to null))
        result shouldBe 0
    }

    @Test
    fun `should throw exception when given invalid inputs`() {
        val expression = $$"$LEN(input1)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        try {
            jsonata.evaluate(mapOf("input1" to mapOf("hello" to "world!")))
        } catch (e: Exception) {
            e.message shouldBe "Argument \"1\" of Object \"LEN\" does not match Object signature"
        }
    }

    @Test
    fun `should throw exception when given number input`() {
        val expression = $$"$LEN(input1)"
        val jsonata = jsonataWithFunctionRegistered(expression)
        try {
            jsonata.evaluate(mapOf("input1" to 111))
        } catch (e: Exception) {
            e.message shouldBe "Argument \"1\" of Object \"LEN\" does not match Object signature"
        }
    }
}