package services.oneteam.ai.flow.execution

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.otSerializer

class FlowExecutionTest {

    @Test
    fun `should deserialize foreach`() {
        val flowExecution = otSerializer.decodeFromString<FlowExecution.ForJson>(
            this::class.java.getResource("/flows/flow-execution-document-foreach.json")!!.readText()
        )
        assertThat(flowExecution).isNotNull
        // subflows
        assertThat(flowExecution.steps[FlowConfiguration.Step.Id("step1")]?.subFlows).isNotEmpty
        assertThat(flowExecution.steps[FlowConfiguration.Step.Id("step1")]?.subFlows["step1_1"]).isNotNull
        // state
        assertThat(flowExecution.state.steps?.entities[FlowConfiguration.Step.Id("step1")]).isNotNull
        assertThat(
            flowExecution.steps[FlowConfiguration.Step.Id("step1")]?.subFlows["step1_1"]?.state?.steps?.entities[FlowConfiguration.Step.Id(
                "subStep1"
            )]
        ).isNotNull
    }

    @Test
    fun `should deserialize filter`() {
        val flowExecution = otSerializer.decodeFromString<FlowExecution.ForJson>(
            this::class.java.getResource("/flows/flow-execution-document-filter.json")!!.readText()
        )
        assertThat(flowExecution).isNotNull
        // subflows
        assertThat(flowExecution.steps[FlowConfiguration.Step.Id("step1")]?.subFlows).isNotEmpty
        assertThat(flowExecution.steps[FlowConfiguration.Step.Id("step1")]?.subFlows["step1_1"]).isNotNull
        // state
        assertThat(flowExecution.state.steps?.entities[FlowConfiguration.Step.Id("step1")]).isNotNull
        assertThat(
            flowExecution.steps[FlowConfiguration.Step.Id("step1")]?.subFlows["step1_1"]?.state?.steps?.entities[FlowConfiguration.Step.Id(
                "filterCondition"
            )]
        ).isNotNull
        assertThat(
            flowExecution.steps[FlowConfiguration.Step.Id("step1")]?.subFlows["step1_1"]?.state?.steps?.entities[FlowConfiguration.Step.Id(
                "filterSetVariables"
            )]
        ).isNotNull
    }

    @Test
    fun `should deserialize aggregate`() {
        val flowExecution = otSerializer.decodeFromString<FlowExecution.ForJson>(
            this::class.java.getResource("/flows/flow-execution-document-aggregate.json")!!.readText()
        )
        assertThat(flowExecution).isNotNull
        // subflows
        assertThat(flowExecution.steps[FlowConfiguration.Step.Id("step1")]?.subFlows).isNotEmpty
        assertThat(flowExecution.steps[FlowConfiguration.Step.Id("step1")]?.subFlows["step1_1"]).isNotNull
        // state
        assertThat(flowExecution.state.steps?.entities[FlowConfiguration.Step.Id("step1")]).isNotNull
        assertThat(
            flowExecution.steps[FlowConfiguration.Step.Id("step1")]?.subFlows["step1_1"]?.state?.steps?.entities[FlowConfiguration.Step.Id(
                "subStep1"
            )]
        ).isNotNull
    }

}