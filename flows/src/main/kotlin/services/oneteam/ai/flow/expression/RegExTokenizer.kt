package services.oneteam.ai.flow.expression

/**
 * Note, does not handle nested tokens.
 */
class RegExTokenizer(val prefix: String = "{{", val suffix: String = "}}") : Tokenizer {
    // can we optimise this to something like `${Regex.escape(prefix)}([a-zA-Z0-9]+?)${Regex.escape(suffix)}`
    private val regex = Regex("(?=${Regex.escape(prefix)})|(?<=${Regex.escape(suffix)})")

    /**
     * Extracts the tokens from the given expression.
     *
     * When the expression is
     *  "This is a {{test}} string with {{multiple}} delimiters.",
     * the tokens will be
     *  ["test", "multiple"].
     */
    override fun tokenize(expression: String): List<String> {
        val tokens = regex.split(expression)
        return tokens.filter { it.startsWith(prefix) && it.endsWith(suffix) }
            .map { it.removePrefix(prefix).removeSuffix(suffix) }
    }

    override fun replace(expression: String, variable: String, value: String): String {
        return expression.replace("$prefix${variable}$suffix", value)
    }

    override fun hasTokens(string: String): Bo<PERSON>an {
        return string.contains(prefix) || string.contains(suffix)
    }

    /**
     * Removes the prefix and suffix from the given string and uses "$$." as the new prefix so that it becomes a
     * reference to the Jsonata context.
     *
     * > $$ represents the root of the input JSON. Only needed if you need to break out of the current context to temporarily
     * > navigate down a different path. E.g. for cross-referencing or joining data.
     * > https://docs.jsonata.org/programming#built-in-variables
     *
     * When the input string is "{{test}}", the output will be "$$.test". This absolute reference should always work
     * since our variables within the {{ }} are always relative to the root of the context.
     *
     * This also avoids issues where jsonata interprets the variable contextually such as in
     * ```
     * Phone[type = {{jsonpath.to.variable}}].number
     * ```
     * See
     * - https://docs.jsonata.org/programming#built-in-variables
     * - JsonataSpike.`should filter by type`()
     * - JsonataSpike.`should filter by type with dynamic selector`()
     */
    fun removePrefixAndSuffix(string: String): String {
        return string.replace(prefix, "$$.").replace(suffix, "")
    }
}