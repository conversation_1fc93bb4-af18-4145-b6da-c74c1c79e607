package services.oneteam.ai.flow.support.pairwise

import org.slf4j.Logger
import org.slf4j.LoggerFactory

class Pairwise(val operation: PairwiseOperation) {

    val logger: Logger = LoggerFactory.getLogger(javaClass)

    /**
     * @param defaultValueFromConfig The default value to use for filling missing values.
     * N.B: [defaultValueFromConfig] will be incorrect for some cases
     * but this is fine since `nulls` cause this, and flow/jsonata context resolves `null` to empty string
     *
     * @param inputs The inputs to perform the operation on.
     * @return The result of the operation, which can be a scalar or a list.
     * @see PairwiseOperation
     * @see PairwiseDefaultValueConfig
     */
    fun operation(defaultValueFromConfig: Any, inputs: Array<out Any?>): Any? {

        if (operation.test(inputs)) {
            return operation.function(inputs)
        }

        return when {

            inputs.all { it is List<*> } -> {
                val maxLength = inputs.filterIsInstance<List<*>>().maxOf { it.size }
                (0 until maxLength).map { index ->
                    recursivePerform(*inputs.map {
                        (it as List<*>).getOrNull(index) ?: List(it.size) { defaultValueFromConfig }
                    }
                        .toTypedArray())
                }
            }

            else -> {
                val filtered = inputs.filter { operation.test(arrayOf(it)) }
                if (filtered.isNotEmpty()) {
                    return operation.function(filtered.toTypedArray())
                }
                return defaultValueFromConfig // used as a fallback
            }
        }
    }

    fun perform(values: List<PairwiseValue>): PairwiseValue {
        require(values.isNotEmpty()) { "Values list cannot be empty" }

        // Start with the first value and add the rest pairwise
        val result = values.map { it.value }.reduce { acc: Any?, value: Any? ->
            recursivePerform(acc, value)
        }

        return PairwiseValue(result!!, values.first().dimension)
    }

    // Recursive function to add lists or numbers
    fun recursivePerform(vararg rawInputs: Any?): Any? {
        logger.trace("Adding inputs: ${rawInputs.joinToString(", ")}")

        // default value for inflation is 0 most of the time
        // except for dates, when it's from config
        // only edge case is for MIN and MAX, when default value for inflation comes from config for dates, and is 0 for numbers
        val inputs = inflateArraysIfRequired(
            *rawInputs
        )
        val defaultValueFromConfig = getDefault(*rawInputs) ?: 0
        logger.trace("Inflated inputs: ${inputs.joinToString(", ")}")
        return operation(defaultValueFromConfig, inputs)
    }

    private fun getDefault(
        vararg inputs: Any?
    ): Any? {
        val defaultConfigs = operation.defaultValueConfig
        val strictDefault = defaultConfigs.firstOrNull { it.strictTest(inputs) }?.defaultValue
        val lenientDefault = defaultConfigs.firstOrNull { it.lenientTest(inputs) }?.defaultValue
        return strictDefault ?: lenientDefault
    }

    fun pairwise(values: List<PairwiseValue>): PairwiseValue {
        require(values.isNotEmpty()) { "Values list cannot be empty" }

        // uplift and add 2 at a time
        var result = values[0]

        for (i in 1 until values.size) {
            val value = values[i]
            val pairwiseValues = listOf(result, value)
            result = perform(upliftToMaxDimension(pairwiseValues))
        }
        return result

    }

    private fun upliftToMaxDimension(values: List<PairwiseValue>): List<PairwiseValue> {
        val maxDimension = values.maxOf { it.dimension }

        // get max length for each dimension so we can uplift all values to the same dimension with the required length
        val maxLengths = IntArray(maxDimension + 1) { 1 }

        for (value in values) {
            for (dim in 0 until value.dimension + 1) {
                maxLengths[dim] = maxOf(maxLengths[dim], value.lengthAtDimension(dim))
            }
        }

        logger.trace("Max length of each dimension {}", maxLengths.toList())
        logger.trace("Uplifting values {}", values)

        // uplift all values to the max dimension
        val upliftedValues = values.map {
            var upliftedValue = it
            while (upliftedValue.dimension < maxDimension) {
                val length = maxLengths[upliftedValue.dimension + 1]
                upliftedValue = upliftedValue.upliftToLength(length)
            }
            upliftedValue
        }
        logger.trace("uplifted values: {}", upliftedValues)

        return upliftedValues
    }

    /**
     * Get the default value to use for the given index when inflating arrays.
     * This is based on the operation's [PairwiseOperation.defaultValueInflateOrigin].
     * 1. If it's ZERO, we use 0.
     * 2. If it's CONFIG_ORDER, we use the full input list to find the default value, done in [inflateArraysIfRequired]
     * 3. If it's CONFIG,
     *      i. We use the default value from the config based on which input is null.
     *      ii. If one input is null, we use the default value for the other paired input in the pairwise operation.
     * Note that if both values are null, this is an error case, and we throw an exception.
     * See [MinPairwiseTest.MixedTypeCases.CornerCases] for test cases that cover [DefaultValueInflateOrigin.CONFIG].
     * See [services.oneteam.ai.flow.expression.functions.EOMonth] tests for test cases that cover [DefaultValueInflateOrigin.CONFIG_ORDER].
     *
     * @param v1 The value from the first input at the current index (or null if not present).
     * @param v2 The value from the second input at the current index (or null if not present).
     * @param input1 The full first input list.
     * @param input2 The full second input list.
     * @return The default value to use for the current index.
     * @throws IllegalArgumentException if both v1 and v2 are null when using CONFIG default value inflate origin.
     */
    private fun getDefaultForIndex(
        v1: Any?, v2: Any?, input1: List<*>, input2: List<*>
    ): Any? {
        return when (operation.defaultValueInflateOrigin) {
            DefaultValueInflateOrigin.ZERO -> 0
            DefaultValueInflateOrigin.CONFIG_ORDER -> null // handled separately
            DefaultValueInflateOrigin.CONFIG -> when {
                v1 == null && v2 != null -> getDefault(v2) ?: getDefault(input2) // need lenient test
                v2 == null && v1 != null -> getDefault(v1) ?: getDefault(input1)
                v1 == null && v2 == null -> throw IllegalArgumentException("Both values are null, not allowed in pairwise") // both operands null shouldn't happen in pairwise
                else -> 0 // both operands not null: not used, see function call
            }

            else -> throw IllegalArgumentException("Unknown default value inflate origin")
        }
    }

    /**
     * If inputs are all lists of numbers (or date strings)
     * and the lists are different lengths
     * we need to make them all the same length by adding the default value
     *
     * @param rawInputs The raw inputs to check and possibly inflate.
     * @return The possibly inflated inputs.
     */
    fun inflateArraysIfRequired(vararg rawInputs: Any?): Array<out Any?> {
        if (rawInputs.size != 2) return rawInputs

        val input1 = rawInputs[0]
        val input2 = rawInputs[1]

        if (input1 is List<*> && input2 is List<*> &&
            input1.all { it !is List<*> } && input2.all { it !is List<*> }
        ) {
            val maxLength = maxOf(input1.size, input2.size)
            val result1 = MutableList<Any?>(maxLength) { null }
            val result2 = MutableList<Any?>(maxLength) { null }

            for (idx in 0 until maxLength) {
                val v1 = input1.getOrNull(idx)
                val v2 = input2.getOrNull(idx)

                if (operation.defaultValueInflateOrigin == DefaultValueInflateOrigin.CONFIG_ORDER) {
                    result1[idx] = v1 ?: getDefault(input1)
                    result2[idx] = v2 ?: getDefault(input2)
                    continue
                }

                val default = getDefaultForIndex(v1, v2, input1, input2)
                result1[idx] = v1 ?: default
                result2[idx] = v2 ?: default
            }
            return arrayOf(result1, result2)
        }

        return rawInputs
    }

}