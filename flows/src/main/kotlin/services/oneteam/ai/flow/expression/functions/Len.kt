package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.expression.coerceToString
import services.oneteam.ai.flow.expression.evaluate
import services.oneteam.ai.flow.expression.listIndexOrNull

@Serializable
internal object Len : ComputeFunction {
    override val key = "len"
    override val syntax = Syntax(
        "LEN(text)", listOf(
            Argument("text", "The text to extract the length from", "string")
        )
    )
    override val description = "Returns the number of characters in a text string"
    override val notes = "e.g. LEN('Hello, world!') returns 13"
    override val category = "text"
    override val icon = Icon("function")
    override val functionName = "LEN"
    override val examples = emptyList<Example>()

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        evaluate(functionName) {
            val text = coerceToString(listIndexOrNull(args, 0))
            text.length
        }
    }, "<s:n>")
}
