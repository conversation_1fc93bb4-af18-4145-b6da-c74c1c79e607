package services.oneteam.ai.flow.expression

import com.dashjoin.jsonata.Jsonata
import kotlinx.datetime.LocalDate

fun checkIfEmpty(value: Any?): Boolean {
    return when (value) {
        null -> true // if value == undefined
        is Boolean -> false
        is String -> value.isEmpty()
        is Number -> checkIfEmpty(value.toString())
        is List<*> -> value.all { element -> checkIfEmpty(element) }
        is Map<*, *> -> value.isEmpty()
        else -> true // if value == null and any other types
    }
}

fun listIndexOrNull(list: List<*>?, index: Int): Any? {
    if (list == null) return null
    if (index < 0 || index >= list.size) return null
    return list[index]
}

fun coerceToString(value: Any?): String {
    return when (value) {
        Jsonata.NULL_VALUE -> ""
        null -> ""
        is String -> value
        is Number -> value.toString()
        is Boolean -> value.toString()
        else -> throw FunctionException("Cannot coerce value to string: $value")
    }
}

fun coerceToLocalDate(value: Any?): LocalDate {
    val text = coerceToString(value)
    try {
        return LocalDate.parse(text)
    } catch (e: Exception) {
        throw FunctionException("Cannot coerce value to LocalDate: $value", e)
    }
}

fun coerceToInt(value: Any?): Int {
    return when (value) {
        is Number -> value.toInt()
        is String -> value.toBigDecimal().toInt()

        else -> throw FunctionException("Cannot coerce value to Int: $value")
    }
}

fun evaluate(name: String, block: () -> Any?): Any? {
    return try {
        block()
    } catch (e: Exception) {
        throw FunctionException("Error during function $name evaluation: ${e.message}", e)
    }
}