package services.oneteam.ai.flow.expression.functions

import com.dashjoin.jsonata.Jsonata.JFunction
import kotlinx.datetime.DatePeriod
import kotlinx.datetime.plus
import kotlinx.serialization.Serializable
import services.oneteam.ai.flow.expression.coerceToInt
import services.oneteam.ai.flow.expression.coerceToLocalDate
import services.oneteam.ai.flow.expression.evaluate

@Serializable
internal object AddDays : ComputeFunction {
    override val key = "addDays"
    override val syntax = Syntax(
        "ADDDAYS(date, days)", listOf(
            Argument("date", "The date to add days to", "string"),
            Argument("days", "The number of days to add", "number")
        )
    )
    override val description = "Add days to a date"
    override val notes = "Add days to a date"
    override val category = "date"
    override val icon = Icon("function")
    override val functionName = "ADDDAYS"
    override val examples = emptyList<Example>()

    @Transient
    override val evaluatorFunction = JFunction({ _, args ->
        evaluate(functionName) {
            val localDate = coerceToLocalDate(args[0])
            val days = coerceToInt(args[1])
            val result = localDate.plus(DatePeriod(days = days)).toString()

            result
        }
    }, "<sx:s>")
}
