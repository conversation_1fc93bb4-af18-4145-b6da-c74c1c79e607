## Renovate

Enabling renovate needs to be managed via https://developer.mend.io/bitbucket/devops-martinit/
Here you can see the current status of each repository and see the PRs that have been created and those that are
pending.

We use renovate to automatically manage our dependencies. Renovate will open pull requests to update dependencies when
new versions are released.
Minor and patch updates are automatically merged, while major updates require manual review.

For more information, see the [Renovate documentation](https://docs.renovatebot.com/).

To configure Renovate, we use the [renovate.json](../renovate.json) file in the root of the repository. For more
information on configuring Renovate, see
the [Renovate configuration documentation](https://docs.renovatebot.com/configuration-options/).

Notes on our configuration:

- We group all updates into a single pull request to reduce noise.
- We ignore updates for certain dependencies that we manage manually, such as `node`.
- We schedule updates to be created at a quiet time avoid disrupting weekday work.
- We automatically merge minor and patch updates to keep our dependencies up to date.
- We use minimumReleaseAge to ensure that new versions have been available for a certain period before updating,
  reducing the risk of issues with newly released versions.
    - https://docs.renovatebot.com/upgrade-best-practices/#wait-two-weeks-before-automerging-third-party-dependencies
    - https://www.bleepingcomputer.com/news/security/self-propagating-supply-chain-attack-hits-187-npm-packages/

Also see

- https://oneteam-services.atlassian.net/wiki/spaces/ONECALC/pages/3539107845/Renovate
- https://docs.renovatebot.com/upgrade-best-practices/#why-updating-often-is-easier-faster-and-safer

