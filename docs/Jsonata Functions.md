# Jsonata Functions

## Overview

We use [Jsonata](https://jsonata.org/) as the expression language for our functions.
Jsonata is a lightweight query and transformation language for JSON data.
It provides a rich set of built-in functions and operators for manipulating JSON data.
We also extend Jsonata with our own custom functions to provide additional functionality.

## The Jsonata Context

We have contextual data which we want to make available to our Jsonata expressions.
These could be forms, foundations, values.
We use a MapBuilder to transform this data into a big JSON object which is then passed to <PERSON>sonata as the context.
This allows our Jsonata expressions to access the contextual data using dot notation (json path).
For example, if we have a form with a field called "name", we can access it in our Jsonata expression as `form.name`.

See FormMapBuilder, FoundationMapBuilder, DefaultMapBuilder, SeriesIntervalMapBuilder for more details on how we build
the context.

## Template variables

In our Jsonata expressions, we may refer to variables in the context.
We reference these variables by wrapping the json path with {{ and }}.
So if we have a context like this:

```json
{
  "form": {
    "name": "<PERSON>"
  }
}
```

We can refer to the name variable in our Jsonata expression as `{{form.name}}`.

If we have a flow context like this:

```json
{
  "global": {
    "workspaceId": 6,
    "workspaceVersionId": 8,
    "tenantId": 1,
    "flowConfigurationId": "WRXHnwzRNXW46Rk5UjHi5",
    "flowConfigurationName": "flow 1"
  },
  "workspace": {
    "documentId": "2sGhzuN2LZWG9iaz7EZes9UU1LHh",
    ...
  },
  "variables": {
    "form": {
      "polyType": "VARIABLE",
      "value": 46,
      "type": "form.aqt",
      "identifier": "form"
    },
    "s1value": {
      "polyType": "VARIABLE",
      "value": 2,
      "type": "number",
      "identifier": "s1value"
    }
  }
}
```

We can use a setVariable step with a Jsonata expression like this to set a variable to the value of s1value * 2:

```text
{{s1value}} * 2
```

In version 1 of our steps, we used string substitution to replace the variables in the expression before passing it to
Jsonata.
So we would replace `{{s1value}}` with `2` and then pass the expression `2 * 2` to Jsonata without any context.
This worked, but it was limited and could lead to issues with more complex expressions, and introduced complexity around
escaping and quoting.

In version 2 of our steps, we replace the `{{` with `$$.` and remove `}}` to convert the expression to a valid Jsonata
expression.
So `{{s1value}} * 2` becomes `$$.s1value * 2`.
We then pass the expression `$$.s1value * 2` to Jsonata with the context containing the variables.
Jsonata will then resolve `$$.s1value` to `2` using the context.

> $$ represents the root of the input JSON. Only needed if you need to break out of the current context to temporarily
> navigate down a different path. E.g. for cross-referencing or joining data.
> https://docs.jsonata.org/programming#built-in-variables

Since the json paths between the `{{` and `}}` are already absolute paths referencing the flow context, we can use $$ to
represent the root of the context.

## Parameters

For our functions that support arrays/pairwise operations as follows.

Assuming our function is SUM:

### When multiple values are passed

```text
$SUM(1,2,3) returns the sum of the numbers, which is 6.
```

### When a single list is passed

```text
$SUM([1,2,3]) returns the sum of the values in the array, which is 6.
```

### When multiple lists are passed

```text
$SUM([1,2,3], [4,5,6]) returns the sum of the pairwise values in the arrays, which is [5, 7, 9].
```

See [Pairwise](pairwise/Pairwise.md) for more details on how pairwise operations work.

## Handling invalid numbers and nulls

For numerical operations, we need to handle invalid numbers and nulls gracefully.
If a value is not a valid number or null, it should be treated as 0 in the context of addition/subtraction and 1 for
multiplication/divide. 
