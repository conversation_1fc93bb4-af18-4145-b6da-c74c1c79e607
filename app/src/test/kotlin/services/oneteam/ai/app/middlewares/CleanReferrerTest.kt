package services.oneteam.ai.app.middlewares

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class CleanReferrerTest {

    @Test
    fun `should clean referrer`() {
        assertThat(cleanReferrer("https://ot.innovation.dev.oneteam.services/ai/workspace/AITR/configuration/flows/pnfBGb3636JVeh7-RxeeX")).isEqualTo(
            "https://ot.innovation.dev.oneteam.services"
        )

        assertThat(cleanReferrer("https://xyz/123")).isEqualTo(
            "https://xyz"
        )
    }
}