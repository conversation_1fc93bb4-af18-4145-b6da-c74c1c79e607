package services.oneteam.ai.app.middlewares

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.util.*
import kotlinx.coroutines.*
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import org.slf4j.LoggerFactory
import services.oneteam.ai.app.logger
import services.oneteam.ai.shared.currentActor
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.EventKey
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.proxy.includeInternalServiceAccount

val EventListKey = AttributeKey<MutableList<Event.ForCreate>>("EventList")
val skipResponseProcessingKey = AttributeKey<Boolean>("SkipResponseProcessing")

const val internalEventEndpoint = "/ai/api/internal/events"
val eventEndpointMapping: Map<EventKey, String> = mapOf(
    EventKey.CREATE_COLLECTION_FOUNDATION to internalEventEndpoint,
    EventKey.UPDATE_COLLECTION_FOUNDATION to internalEventEndpoint,
    EventKey.DELETE_COLLECTION_FOUNDATION to internalEventEndpoint,
    EventKey.CREATE_COLLECTION_FORM to internalEventEndpoint,
    EventKey.UPDATE_COLLECTION_FORM to internalEventEndpoint,
    EventKey.DELETE_COLLECTION_FORM to internalEventEndpoint,
    EventKey.UPDATE_COLLECTION_FORM_ANSWER to internalEventEndpoint,
    EventKey.START_FLOW_MANUALLY_FROM_FORM to internalEventEndpoint,
    EventKey.START_FLOW_MANUALLY_FROM_FOUNDATION to internalEventEndpoint,
    EventKey.RECEIVE_INCOMING_WEBHOOK_FLOW to internalEventEndpoint,
    EventKey.RECEIVE_INCOMING_WEBHOOK_WORKSPACE to internalEventEndpoint,
)

private fun getEventEndpoint(eventKey: EventKey): String {
    return eventEndpointMapping[eventKey]
        ?: throw IllegalArgumentException("No endpoint mapping found for event key: $eventKey")
}

fun EventMiddleware(proxyService: ProxyService) = createApplicationPlugin(name = "EventMiddleware") {

    onCall { call ->
        call.attributes.put(EventListKey, mutableListOf())
        val status = call.response.status()
        if (status == null || !status.isSuccess()) {
            call.attributes.put(skipResponseProcessingKey, true)
        }
    }

    onCallRespond { call ->
        val logger = LoggerFactory.getLogger(javaClass)

        if (!call.attributes[skipResponseProcessingKey]) {
            logger.info("EventMiddleware skipping response processing")
            return@onCallRespond
        }

        val eventList = call.attributes[EventListKey]
        if (eventList.isEmpty()) {
            return@onCallRespond
        }

        eventList.forEach { event -> sendEventToQueue(proxyService, event) }
        logger.info("EventMiddleware sent all event to queue")
    }
}


suspend fun sendEventToQueue(proxyService: ProxyService, eventPayload: Event.ForCreate) {
    val actor = currentActor()
    @OptIn(DelicateCoroutinesApi::class)
    // this is to employ a 'fire and forget' strategy but still leverage contextual data and safety of supervised scope
    GlobalScope.launch(currentCoroutineContext()) {
        supervisorScope {
            launch {
                val eventKey = eventPayload.eventProperties.key
                val tenantOrigin = ProxyService.tenantOriginUrl()
                val requestPayload = ProxyService.ProxyEndpointBody(
                    url = ProxyService.buildInternalTenantUrl(endpoint = getEventEndpoint(eventKey)),
                    headers = mapOf(
                        HttpHeaders.Origin to tenantOrigin,
                    ),
                    method = "POST",
                    body = ProxyService.ProxyRequestBody.Json(
                        Json.encodeToJsonElement(
                            serializer = Event.ForCreate.serializer(),
                            eventPayload
                        ).jsonObject
                    ),
                ).includeInternalServiceAccount()

                logger.info("EventMiddleware sending event to queue: $eventPayload")
                proxyService.call(requestPayload, actor)
            }
        }
    }
    logger.info("EventMiddleware sent event to queue: $eventPayload")
}
