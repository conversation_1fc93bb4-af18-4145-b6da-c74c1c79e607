package services.oneteam.ai.app.domains.webhook

import io.ktor.http.*
import io.ktor.resources.*
import io.ktor.server.request.*
import io.ktor.server.resources.post
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import kotlinx.serialization.json.JsonObject
import services.oneteam.ai.app.middlewares.addEventToCallAttributes
import services.oneteam.ai.shared.currentActor
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService

@Suppress("unused")
@Resource("/webhooks")
private class WebhookRoute() {

    @Resource("/workspaces/{workspaceId}")
    class WorkspaceId(var workspaceId: Workspace.Id, val parent: WebhookRoute) {

        @Resource("/flows/{flowConfigurationId}")
        class Flow(var flowConfigurationId: FlowConfiguration.Id, val parent: WorkspaceId)
    }
}

fun Route.externalWebhookEndpoints(
    workspaceVersionService: WorkspaceVersionService
) {
    post<WebhookRoute.WorkspaceId> { routeParams ->
        val payload = call.receive<JsonObject>()
        val triggerIdentifier = call.queryParameters["tid"]
        val workspaceId = routeParams.workspaceId

        val activeWorkspaceVersion = workspaceVersionService.getActiveWorkspaceVersion(workspaceId)
        if (activeWorkspaceVersion == null) {
            call.respond(HttpStatusCode.BadRequest, "Workspace not found")
            return@post
        }

        val eventPayload = Event.ForCreate(
            workspaceId = workspaceId,
            eventProperties = Event.EventProperties.ReceiveIncomingWebhookForWorkspace(
                actor = currentActor(),
                payload = payload,
                triggerIdentifier = triggerIdentifier
            )
        )

        addEventToCallAttributes(call, eventPayload)

        call.respond(HttpStatusCode.OK, mapOf<String, String>())
    }

    post<WebhookRoute.WorkspaceId.Flow> { routeParams ->
        val payload = call.receive<JsonObject>()
        val workspaceId = routeParams.parent.workspaceId
        val flowConfigurationId = routeParams.flowConfigurationId

        val activeWorkspaceVersion = workspaceVersionService.getActiveWorkspaceVersion(workspaceId)
        if (activeWorkspaceVersion == null) {
            call.respond(HttpStatusCode.BadRequest, "Workspace not found")
            return@post
        }
        if (!activeWorkspaceVersion.configuration.flows.entities.contains(flowConfigurationId)) {
            call.respond(HttpStatusCode.BadRequest, "Flow not found")
            return@post
        }

        val eventPayload = Event.ForCreate(
            workspaceId = workspaceId,
            eventProperties = Event.EventProperties.ReceiveIncomingWebhookForFlow(
                actor = currentActor(),
                payload = payload,
                flowConfigurationId = flowConfigurationId.value,
            )
        )

        addEventToCallAttributes(call, eventPayload)

        call.respond(HttpStatusCode.OK, mapOf<String, String>())
    }
}
