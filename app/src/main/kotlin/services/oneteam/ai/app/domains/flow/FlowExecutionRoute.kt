package services.oneteam.ai.app.domains.flow

import io.ktor.resources.*
import io.ktor.server.request.*
import io.ktor.server.resources.*
import io.ktor.server.resources.post
import io.ktor.server.response.*
import io.ktor.server.routing.*
import services.oneteam.ai.app.extensions.buildPageRequest
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.pubsub.MessageType
import services.oneteam.ai.flow.pubsub.PubSubService
import services.oneteam.ai.flow.pubsub.WebSocketMessage
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.coroutineContext

@Resource("/workspaces")
class FlowExecutionRoute() {

    @Resource("{workspaceId}")
    class Id(val parent: FlowExecutionRoute, val workspaceId: Workspace.Id) {
        @Resource("/flow")
        class CreateFlowExecution(val workspaceId: Id)

        @Resource("/flow/run")
        class RunOldestFlowExecution(val workspaceId: Id)

        @Resource("/flowExecutions")
        class FlowExecutionsRoute(val workspaceId: Id) {
            @Resource("/{flowExecutionId}")
            class FlowExecutionId(val parent: FlowExecutionsRoute, val flowExecutionId: FlowExecution.Id) {
                @Resource("/execute")
                class RunFlowExecution(
                    val parent: FlowExecutionId,
                    val flowExecutionId: FlowExecution.Id,
                    val workspaceId: Workspace.Id
                )

                @Resource("/cancel")
                class CancelFlowExecution(
                    val parent: FlowExecutionId,
                    val flowExecutionId: FlowExecution.Id
                )

                @Resource("/view")
                class ViewFlowExecution(
                    val parent: FlowExecutionId,
                    val flowExecutionId: FlowExecution.Id
                )

                @Resource("/configuration")
                class FlowExecutionConfiguration(
                    val parent: FlowExecutionId,
                    val flowExecutionId: FlowExecution.Id,
                    val workspaceId: Workspace.Id
                )
            }
        }

    }
}

fun Route.publicFlowExecutionEndpoints(flowExecutionService: FlowExecutionService) {

    get<FlowExecutionRoute.Id.FlowExecutionsRoute> { flowParams ->
        val pageRequest = call.request.buildPageRequest(
            FlowExecutionRepository.SORTABLE_FIELDS,
            FlowExecutionRepository.DEFAULT_SORTABLE_FIELDS
        )
        val searchTerm = call.request.queryParameters["search"]
        val status = call.request.queryParameters["status"]
            ?.let { FlowExecution.Status.valueOf(it) }
        val result = call.request.queryParameters["result"]
            ?.let { FlowExecution.Result.valueOf(it) }

        val pageResult = flowExecutionService.search(
            pageRequest,
            searchTerm,
            flowParams.workspaceId.workspaceId,
            status,
            result,
        )
        call.respond(pageResult)
    }

    get<FlowExecutionRoute.Id.FlowExecutionsRoute.FlowExecutionId> { flowParams ->
        val flow = flowExecutionService.get(flowParams.flowExecutionId)
        call.respond(FlowResponse(success = true, data = flow))
    }

    post<FlowExecutionRoute.Id.FlowExecutionsRoute.FlowExecutionId.CancelFlowExecution> { flowParams ->
        val principalId = coroutineContext[RequestContext]?.principalId()
        flowExecutionService.cancelFlowExecution(flowParams.flowExecutionId, principalId!!)
        call.respond(FlowResponse(success = true, data = null))
    }

    get<FlowExecutionRoute.Id.FlowExecutionsRoute.FlowExecutionId.ViewFlowExecution> { flowParams ->
        val isRedirect = call.request.queryParameters["redirect"]?.toBoolean() == true
        val url = flowExecutionService.getViewURL(flowParams.flowExecutionId)
        if (isRedirect) {
            call.respondRedirect(url)
        } else {
            call.respond(mapOf("url" to url))
        }
    }

    get<FlowExecutionRoute.Id.FlowExecutionsRoute.FlowExecutionId.FlowExecutionConfiguration> { flowParams ->
        val configuration = flowExecutionService.getWorkspaceConfigurationVersion(
            flowParams.flowExecutionId,
            flowParams.workspaceId
        )
        call.respond(configuration)
    }
}

fun Route.twoTypeAuthFlowExecutionEndpoints(flowExecutionService: FlowExecutionService) {
    post<FlowExecutionRoute.Id.FlowExecutionsRoute.FlowExecutionId.RunFlowExecution> { flowParams ->
        val flow: FlowExecution.ForApi? =
            flowExecutionService.runFlowByExecutionId(
                flowParams.flowExecutionId,
                flowParams.workspaceId,
            )
        call.respond(FlowResponse(success = true, data = flow))
    }
}

fun Route.internalFlowExecutionEndpoints(flowExecutionService: FlowExecutionService) {
    post<FlowExecutionRoute.Id.RunOldestFlowExecution> { flowParams ->
        val flow: FlowExecution.ForApi? =
            flowExecutionService.runOldestFlow(flowParams.workspaceId.workspaceId)
        call.respond(FlowResponse(success = true, data = flow))
    }

    post<FlowExecutionRoute.Id.CreateFlowExecution>
    { flowParams ->
        val body = call.receive<CreateFlowExecutionRequestBody>()

        val flow = if (body.creationMode == CreateFlowExecutionRequestBody.CreationMode.AVOID_DUPLICATION) {
            flowExecutionService.createIfNoDuplicates(flowParams.workspaceId.workspaceId, body)
        } else {
            flowExecutionService.create(flowParams.workspaceId.workspaceId, body)
        }

        PubSubService.sendToWorkspace(
            flowParams.workspaceId.workspaceId,
            WebSocketMessage(MessageType.FlowRunnerUpdate)
        )
        call.respond(FlowResponse(success = true, data = flow))
    }
}

