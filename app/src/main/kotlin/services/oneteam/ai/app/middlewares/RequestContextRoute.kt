package services.oneteam.ai.app.middlewares

import io.ktor.server.application.*
import io.ktor.server.routing.*
import io.ktor.server.sessions.*
import io.ktor.util.pipeline.*
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.withContext
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.app.AppMDC
import services.oneteam.ai.shared.UserSession
import services.oneteam.ai.shared.domains.event.constructActorFromHeaders
import services.oneteam.ai.shared.hasCurrentTransaction
import services.oneteam.ai.shared.helpers.CustomNanoId
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.CoroutineContext

val logger: Logger = LoggerFactory.getLogger("Middleware")

/**
 * The configuration for the request context config
 */
class RequestContextRouteConfig

internal object RequestContextRouteHook : Hook<suspend (ApplicationCall, suspend () -> Unit) -> Unit> {
    internal val RequestContextPhase: PipelinePhase = PipelinePhase("RequestContextPhase")

    override fun install(
        pipeline: ApplicationCallPipeline,
        handler: suspend (ApplicationCall, suspend () -> Unit) -> Unit,
    ) {
        pipeline.insertPhaseAfter(ApplicationCallPipeline.Plugins, RequestContextPhase)
        pipeline.intercept(RequestContextPhase) { handler(call, ::proceed) }
    }
}

/**
 * A plugin that process calls with request context. Usually used via the [withRequestContext] function inside routing.
 */
val RequestContextRoutePlugin: RouteScopedPlugin<RequestContextRouteConfig> =
    createRouteScopedPlugin("RequestContextRoutePlugin", ::RequestContextRouteConfig) {
        on(RequestContextRouteHook) { call, block -> withRequestContext(call, block) }
    }

fun Route.withRequestContext(build: Route.() -> Unit): Route {
    val route = this
    route.install(RequestContextRoutePlugin)
    route.build()
    return route
}

internal suspend inline fun withRequestContext(
    call: ApplicationCall,
    crossinline block: suspend () -> Unit,
) {

    call.configureLogging()
    call.extendSessionTimeout()

    require(!hasCurrentTransaction()) { "Transaction should not exist at beginning of request" }

    val requestContext = call.toRequestContext()
    logger.trace("Request context {}", requestContext)

    withContext(MDCContext() + requestContext) {
        block()
    }

}

private fun ApplicationCall.extendSessionTimeout() {
    val userSession = this.sessions.get<UserSession>()
    if (userSession != null) {
        this.sessions.set(userSession.copy(maxTime = System.currentTimeMillis() + 1000 * 60 * 60))
    }
}

/**
 * Support MDC for logging
 *   https://github.com/Kotlin/kotlinx.coroutines/tree/master/integration/kotlinx-coroutines-slf4j
 */
private fun ApplicationCall.configureLogging() {
    AppMDC.withTenant(this.attributes[tenantKey])
    AppMDC.withRequestId(CustomNanoId.generate())
}

fun ApplicationCall.toRequestContext(): CoroutineContext = RequestContext(
    tenant = this.attributes[tenantKey],
    principal = this.sessions.get<UserSession>(),
    actor = constructActorFromHeaders(this.request.headers, this.sessions.get<UserSession>()?.userId)
)


