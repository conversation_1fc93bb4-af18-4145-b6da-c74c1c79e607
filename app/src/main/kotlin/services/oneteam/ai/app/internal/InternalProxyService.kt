package services.oneteam.ai.app.internal

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.utils.io.*
import kotlinx.coroutines.currentCoroutineContext
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import runInternalRequest
import services.oneteam.ai.shared.common.OTAIHeaders
import services.oneteam.ai.shared.domains.event.EventActor
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.middlewares.RequestContext

class InternalProxyService(
    val application: Application, private val serviceAccountToken: String
) : ProxyService {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun call(
        body: ProxyService.ProxyEndpointBody,
        actor: EventActor,
        isExternalResponse: Boolean?,
        timeoutMillis: Long? // only used in ExternalProxyService for now
    ): ProxyService.ProxyEndpointResponse {
        try {
            val tenant = currentCoroutineContext()[RequestContext]!!.tenant
            val url = Url(body.url)
            val call = application.runInternalRequest {
                method = HttpMethod.parse(body.method)
                uri = url.fullPath
                scheme = url.protocol.name
                version = "HTTP/1.1"
                port = url.port
                host = url.host
                if (body.body != null) {
                    require(body.body is ProxyService.ProxyRequestBody.Json) {
                        throw IllegalArgumentException("Only JSON body is supported in InternalProxyService")
                    }
                    <EMAIL> = ByteReadChannel(
                        (body.body as ProxyService.ProxyRequestBody.Json).value.toString().toByteArray()
                    )
                }

                requestHeaders = mutableMapOf<String, String>().apply {
                    putAll(body.headers ?: emptyMap())
                    putAll(body.authentication ?: emptyMap())
                    putAll(body.options ?: emptyMap())
                    if (body.body != null) {
                        // default to JSON
                        put(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                    }
                    put(HttpHeaders.Referrer, tenant.originUrl)

                    if (body.authentication?.get("useOtaiServiceAccount") == "true") {
                        put(HttpHeaders.Authorization, "Bearer $serviceAccountToken")
                    }
                    put(OTAIHeaders.X_OTAI_FLOW, actor.serializeToString())
                    put(OTAIHeaders.X_OTAI_ACTOR, actor.serializeToString())
                }
            }
            val response = call.response

            val isSuccess = response.status()?.value.toString().substring(0, 1) == "2"
            if (isSuccess) {
                logger.info("Internal call to ${body.url} was successful")
            } else {
                logger.error("Internal call to ${body.url} failed with status ${response.status()?.value}")
            }

            val responseContent = call.response.byteContent?.decodeToString()
            return ProxyService.ProxyEndpointResponse(
                response = responseContent,
                status = if (isSuccess) ProxyService.ProxyEndpointResponseStatus.SUCCESS else ProxyService.ProxyEndpointResponseStatus.FAIL,
                error = if (!isSuccess) responseContent else null
            )
        } catch (e: Exception) {
            logger.error("Internal call to ${body.url} failed with error: ${e.message}", e)
            return ProxyService.ProxyEndpointResponse(
                response = null, status = ProxyService.ProxyEndpointResponseStatus.FAIL, error = e.message
            )
        }
    }
}