# General

This is a Kotlin project built with Gradle.

## Folder Structure

There are multiple modules

- permissions
- shared
- flows
- app


- `/docs`: Contains documentation for the project, including API specifications and user guides.

## Libraries and Frameworks

* Kotlin
* KTOR
* Exposed
* Postgres

* JUnit
* AssertJ

Bruno is used for manual api testing.

# Tests

Tests are run with JUnit.
Asserts are done with AssertJ.
When appropriate tests should be data driven.

Example test for exceptions:

```kotlin
   @Test
fun `should error due to date format`() {
    val expression = $$"""$ADDDAYS("abc", 650)"""
    val exception = assertThrows<RuntimeException> {
        jsonataWithFunctionRegistered(expression).evaluate(null)
    }
    assertThat(exception.message)
        .contains("Error during function ADDDAYS evaluation: Cannot coerce value to LocalDate: abc")
}
```

Example of a simple data driven (parameterized) test:

```kotlin
    @ParameterizedTest
@CsvSource(
    value = [
        $$"""$ADDDAYS("2020-01-01", 650); 2021-10-12 """,
        $$"""$ADDDAYS("2020-02-29", 365); 2021-02-28 """,
        $$"""$ADDDAYS("2020-02-29", 366); 2021-03-01 """,
        $$"""$ADDDAYS("2020-01-31", 1); 2020-02-01 """,
        $$"""$ADDDAYS("2020-01-31", -1); 2020-01-30 """,
        $$"""$ADDDAYS("2020-03-31", -31); 2020-02-29 """,
        $$"""$ADDDAYS("2019-03-31", -31); 2019-02-28 """,
    ],
    delimiter = ';'
)
fun `should add days to date`(expression: String, expected: String) {
    val result = jsonataWithFunctionRegistered(expression).evaluate(null)
    assertThat(result).isEqualTo(expected)
}
```

Example of a more complex data driven (parameterized) test where we define a Spec data class to hold input and expected
values and provider functions to generate test data:

```kotlin
class AddDaysTest {

    data class Spec(
        val input: String,
        val expected: String,
    )

    companion object {
        @JvmStatic
        fun validProvider(): List<Spec> {
            return listOf(
                Spec(input = $$"""$ADDDAYS("2020-01-01", 650)""", expected = "2021-10-12")
            )
        }

        @JvmStatic
        fun exceptionProvider(): List<Spec> {
            return listOf(
                Spec(
                    input = $$"""$ADDDAYS("abc", 650)""",
                    expected = "Error during function ADDDAYS evaluation: Cannot coerce value to LocalDate: abc"
                ),
                Spec(
                    input = $$"""$ADDDAYS("2020-01-01", "abc")""",
                    expected = """Error during function ADDDAYS evaluation: Character a is neither a decimal digit number, decimal point, nor "e" notation exponential mark."""
                ),
            )
        }
    }

    @ParameterizedTest
    @MethodSource("validProvider")
    fun `should add days`(spec: Spec) = runTest {
        val result = jsonataWithFunctionRegistered(spec.input).evaluate(null)
        assertThat(result).isEqualTo(spec.expected)
    }

    @ParameterizedTest
    @MethodSource("exceptionProvider")
    fun `should throw error when adding days`(spec: Spec) = runTest {
        val exception = assertThrows<Exception> {
            jsonataWithFunctionRegistered(spec.input).evaluate(null)
        }
        assertThat(exception.message).contains(spec.expected)
    }
}
```

## Scripts

We write scripts in javascript and run them with zx. A simple example script:

```javascript
#!/usr/bin/env zx

console.log('Hello, World!')
```

If you are asked to write a script prefer zx over bash or other shell scripting languages unless there is a very good
reason not to.