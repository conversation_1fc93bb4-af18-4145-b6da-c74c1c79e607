// Unit test for use cases in the OT Data Model:
// https://www.figma.com/board/AObyvNSSZk77qjQw7G9KXD/OT-Data-Modelling?node-id=2308-2752&t=XMFkWlYKjCKdH9Am-4


definition tenant {}

definition group {
    relation member: subject

    permission is_member = member
}

definition subject {
  relation tenant: tenant
}

definition workspace {
    relation tenant: tenant

    relation collection: subject | group
    relation configuration: subject | group
    relation settings: subject | group

    permission has_collection = collection
    permission has_configuration = configuration
    permission has_settings = settings

    permission view = collection + configuration + settings
}

definition foundation {
    relation tenant: tenant
    relation workspace: workspace

    relation parent: foundation | workspace
    relation inherit_from: foundation

    relation manager: subject | group
    relation read: subject | group
    relation write: subject | group

    permission view = manager + read + read->is_member + write + inherit_from->view
    permission edit = manager + write + write->is_member + inherit_from->edit
    permission manage = manager + manager->is_member + inherit_from->manage

}

definition form {
    relation tenant: tenant
    relation workspace: workspace
    relation parent: foundation
    relation inherit_from: foundation

    // https://authzed.com/docs/spicedb/modeling/attributes
    relation public: subject:*

    relation manager: subject | group
    relation read: subject | group
    relation write: subject | group

    permission view =  public + manager + read + read->is_member + write + inherit_from->view
    permission edit = manager + write + write->is_member + inherit_from->edit
    permission manage = manager + manager->is_member + inherit_from->manage

}

