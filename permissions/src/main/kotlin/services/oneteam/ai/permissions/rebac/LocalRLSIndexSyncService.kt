package services.oneteam.ai.permissions.rebac

import kotlinx.serialization.Serializable
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.permissions.PermissionsException
import kotlin.system.measureTimeMillis

@Serializable
data class ValidationResult(
    val missingInLocalIndex: List<Long>, val missingInRebac: List<Long>
)

/**
 * This class uses the ReBAC system to update the local RLS index.
 * The local RLS index is required to allow filtering and sorting which is not possible with a ReBAC system alone.
 *
 * It finds the resources that a subject can view and updates the local index accordingly.
 * This local index is used to enforce RLS in the application.
 */
class LocalRLSIndexSyncService(
    private val rebacRepository: RebacRepository,
    private val localIndexRepository: SubjectResourcePermissionsRepository,
    private val resourceKeys: Map<Resource, Int>
) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    fun permissionStateForResource(resourceId: Long, resourceType: Resource): PermissionState {
        return PermissionState(
            "Permission state for resource TYPE ${resourceType.key}, resource ID $resourceId",
            rebacRepository.readRelationships(
                resourceType, resourceId.toString()
            ).map { it.toString() }.sorted(),
            localIndexRepository.findAllForResource(
                resourceTypeId = resourceKeys.getValue(resourceType),
                resourceId = resourceId,
            )
                .map { it.copy(resourceType = resourceKeys.entries.find { entry -> entry.value == it.resourceType.toInt() }!!.key.key) }
                .map { it.toString() }.sorted()
        )

    }

    /**
     * Validates that the local index is in sync with the ReBAC system for all resource types.
     * Returns a map of resource type to validation result.
     */
    fun validateViewBySubject(tenantId: Long, subjectId: Long): Map<Resource, ValidationResult> {
        val map = mutableMapOf<Resource, ValidationResult>()
        resourceKeys.keys.forEach { resourceKey ->
            map[resourceKey] = validateViewBySubject(tenantId, subjectId, resourceKey)
        }
        return map
    }

    fun validateViewBySubject(tenantId: Long, subjectId: Long, resourceType: Resource): ValidationResult {
        // find all the resources of the given type that the subject can view
        val inRebac = rebacRepository.findAllResourcesWithPermissionForSubject(
            resourceType = resourceType,
            subjectType = PermissionStructure.Subject.NAME,
            subjectId = subjectId.toString(),
            permission = getViewPermissionForResource(resourceType)
        ).map { resource -> resource.toLong() }

        // get resources for the given resource type from local index repository
        val inLocalIndex = localIndexRepository.findAllForSubject(
            resourceKeys.getValue(resourceType), subjectId = subjectId, tenantId = tenantId
        )

        // find the resources that are in rebac but not in local index
        val missingInLocalIndex = inRebac.filter { resourceId -> !inLocalIndex.any { it == resourceId } }
        if (missingInLocalIndex.isNotEmpty()) {
            logger.warn(
                "Missing resources in local index for tenant $tenantId, subject ID $subjectId, resource type $resourceType: $missingInLocalIndex"
            )
        } else {
            logger.info(
                "All resources for tenant $tenantId, subject ID $subjectId, resource type $resourceType are present in local index"
            )
        }
        // find the resources that are in local index but not in rebac
        val missingInRebac = inLocalIndex.filter { resourceId -> !inRebac.any { it == resourceId } }
        if (missingInRebac.isNotEmpty()) {
            logger.warn(
                "Missing resources in ReBAC for tenant $tenantId, subject ID $subjectId, resource type $resourceType: $missingInRebac"
            )
        } else {
            logger.info(
                "All resources for tenant $tenantId, subject ID $subjectId, resource type $resourceType are present in ReBAC"
            )
        }

        return ValidationResult(
            missingInLocalIndex = missingInLocalIndex, missingInRebac = missingInRebac
        )
    }

    /**
     * Replace all the rows in the local index for a given subject.
     */
    fun syncViewBySubject(tenantId: Long, subjectId: Long, token: String? = null): Map<Resource, Int> {

        logger.info("Syncing permissions for tenant $tenantId, subject ID $subjectId")

        val map = mutableMapOf<Resource, Int>()

        // for each resource type that uses RLS, find the resources that the subject can view
        // and update the local index accordingly
        resourceKeys.keys.forEach { resourceKey ->
            try {
                val result = syncViewBySubjectAndResourceType(tenantId, subjectId, resourceKey, token)
                map[resourceKey] = result
            } catch (e: Exception) {
                logger.error(
                    "Failed to sync permissions for tenant $tenantId, subject ID $subjectId, resource type $resourceKey",
                    e
                )
                throw e
            }
        }
        logger.trace(
            "Finished syncing permissions for tenant {}, subject ID {} with results: {}", tenantId, subjectId, map
        )
        return map
    }

    /**
     * Syncs all the permissions for a given user and resource type.
     */
    fun syncViewBySubjectAndResourceType(
        tenantId: Long, subjectId: Long, resourceType: Resource, token: String? = null
    ): Int {
        logger.info("Starting sync for tenant $tenantId, subject ID $subjectId, resource type $resourceType")
        var resourceCount = 0
        val time = measureTimeMillis {
            try {
                val resources = rebacRepository.findAllResourcesWithPermissionForSubject(
                    resourceType = resourceType,
                    subjectType = PermissionStructure.Subject.NAME,
                    subjectId = subjectId.toString(),
                    permission = getViewPermissionForResource(resourceType),
                    token = token
                ).map { it.toLong() }


                resourceCount = resources.size

                localIndexRepository.updateForSubjectAndResources(
                    resources, resourceKeys.getValue(resourceType), subjectId, tenantId
                )
            } catch (e: Exception) {
                throw PermissionsException(
                    "Error updating local index for tenant $tenantId, subject ID $subjectId, resource type $resourceType",
                    e
                )
            }

        }
        logger.info("Synced $resourceCount permissions for tenant $tenantId, subject ID $subjectId, resource type $resourceType in $time ms")
        return resourceCount

    }

    private fun getViewPermissionForResource(resourceType: Resource): Permission =
        PermissionStructure.SYNC_VIEW_MAP[resourceType]
            ?: throw IllegalArgumentException("No VIEW permission defined for resource type $resourceType")

    /**
     * Syncs all the permissions for a given resource and resource type.
     */
    fun syncViewByResource(
        tenantId: Long, resourceId: Long, resourceType: Resource, token: String? = null
    ): Int {
        var subjectCount = 0

        val time = measureTimeMillis {
            try {
                rebacRepository.findAllSubjectsWithPermissionOnResource(
                    resourceType = resourceType,
                    resourceId = resourceId.toString(),
                    permission = getViewPermissionForResource(resourceType),
                    token = token
                ).let { subjects ->

                    logger.info("Syncing ${subjects.size} permissions for tenant $tenantId, resource type $resourceType, resource ID $resourceId")
                    subjectCount = subjects.size

                    // do the update even if there are no subjects so that we remove any stale entries
                    localIndexRepository.updateForResourceAndSubjects(
                        resourceId = resourceId,
                        resourceTypeId = resourceKeys.getValue(resourceType),
                        subjectIds = subjects.map { it.toLong() },
                        tenantId = tenantId
                    )
                }
            } catch (e: Exception) {
                throw PermissionsException(
                    "Error updating local index for tenant $tenantId, resource ID $resourceId, resource type $resourceType",
                    e
                )
            }
        }
        logger.info("Synced permissions for tenant $tenantId, resource type $resourceType, resource ID $resourceId in $time ms")
        return subjectCount
    }

    fun syncViewByResources(
        tenantId: Long, token: String?, affectedResources: List<ResourceCollection>
    ) {
        affectedResources.forEach { resourceCollection ->
            resourceCollection.ids.forEach { resourceId ->
                try {
                    syncViewByResource(
                        tenantId = tenantId,
                        resourceId = resourceId,
                        resourceType = resourceCollection.resourceType,
                        token = token
                    )
                } catch (e: Exception) {
                    logger.error(
                        "Failed to sync permissions for tenant ${tenantId}, resource ID $resourceId, resource type ${resourceCollection.resourceType}",
                        e
                    )
                }
            }
        }
    }
}

data class PermissionState(
    val title: String, val inRebac: List<String>, val inLocalIndex: List<String>
) {
    override fun toString(): String {
        return """
$title
            
In ReBAC (${inRebac.size}):
${inRebac.joinToString("\n") { it.trim() }}
            
In Local Index (${inLocalIndex.size}):
${inLocalIndex.joinToString("\n") { it.trim() }}

        """
    }
}