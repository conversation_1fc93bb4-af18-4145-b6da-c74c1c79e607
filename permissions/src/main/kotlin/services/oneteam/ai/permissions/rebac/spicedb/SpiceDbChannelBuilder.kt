package services.oneteam.ai.permissions.rebac.spicedb

import io.grpc.ManagedChannel
import io.grpc.ManagedChannelBuilder
import java.util.concurrent.TimeUnit

/**
 * A builder for creating a gRPC channel to connect to SpiceDB.
 *
 * You should keep the gRPC channel open and reuse it across many threads or coroutines.
 * gRPC channels are designed to be thread-safe and efficient for multiple concurrent requests.
 * Creating a new channel for every request is resource-intensive and can lead to connection exhaustion and performance issues.
 * Reuse a single channel and create new stubs as needed for each request.
 *
 * @param token The authentication token to use for the connection (same as that used in the server).
 * @param target The target address of the SpiceDB server.
 * @param usePlaintext Whether to use plaintext (non-TLS) connection.
 */
class SpiceDbChannelBuilder(private val token: String, private val target: String, private val usePlaintext: Boolean) {

    fun build(): CloseableChannel {
        check(!(token.isEmpty() || target.isEmpty())) { "SpiceDbChannelBuilder must be initialized with token and target before building the channel." }

        val channelBuilder = ManagedChannelBuilder.forTarget(target)

        if (usePlaintext) {
            channelBuilder.usePlaintext() // if not using TLS, replace with .usePlaintext()
        } else {
            channelBuilder.useTransportSecurity() // if not using TLS, replace with .usePlaintext()
        }

        return CloseableChannel(channelBuilder.build())
    }
}

class CloseableChannel(val channel: ManagedChannel) : AutoCloseable {
    override fun close() {
        channel.shutdown()
        try {
            if (!channel.awaitTermination(5, TimeUnit.SECONDS)) {
                channel.shutdownNow()
            }
        } catch (e: InterruptedException) {
            channel.shutdownNow()
        }
    }
}