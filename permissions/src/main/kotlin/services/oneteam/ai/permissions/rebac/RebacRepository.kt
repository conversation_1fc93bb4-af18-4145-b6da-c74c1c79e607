package services.oneteam.ai.permissions.rebac

import kotlinx.serialization.Serializable
import services.oneteam.ai.permissions.rebac.spicedb.RelationshipModel

@JvmInline
@Serializable
value class Resource(val key: String)

@JvmInline
@Serializable
value class Relationship(val key: String)

@JvmInline
@Serializable
value class Permission(val key: String)

/**
 * This object defines the structure of permissions and relationships used in the REBAC system.
 * It directly mirrors the schema eg schema.zed
 */
object PermissionStructure {

    val SYNC_VIEW_MAP = mapOf(
        Workspace.NAME to Workspace.Permissions.VIEW,
        Foundation.NAME to Foundation.Permissions.VIEW,
        Form.NAME to Form.Permissions.VIEW
    )

    object Tenant {
        val NAME = Resource("tenant")
    }

    object Subject {
        val NAME = Resource("subject")
    }

    object Workspace {
        val NAME = Resource("workspace")

        object Relationships {
            val TENANT = Relationship("tenant")

            val COLLECTION = Relationship("collection")
            val CONFIGURATION = Relationship("configuration")
            val SETTINGS = Relationship("settings")
        }

        object Permissions {
            val HAS_COLLECTION = Permission("has_collection")
            val HAS_CONFIGURATION = Permission("has_configuration")
            val HAS_SETTINGS = Permission("has_settings")
            val VIEW = Permission("view")
        }
    }

    object Foundation {
        val NAME = Resource("foundation")

        object Relationships {
            val TENANT = Relationship("tenant")
            val WORKSPACE = Relationship("workspace")

            val PARENT = Relationship("parent")
            val INHERIT_FROM = Relationship("inherit_from")

            val MANAGER = Relationship("manager")
            val WRITE = Relationship("write")
            val READ = Relationship("read")
        }

        object Permissions {
            val VIEW = Permission("view")
            val EDIT = Permission("edit")
            val MANAGE = Permission("manage")
        }
    }

    object Form {
        val NAME = Resource("form")

        object Relationships {
            val TENANT = Relationship("tenant")
            val WORKSPACE = Relationship("workspace")

            val PARENT = Relationship("parent")
            val INHERIT_FROM = Relationship("inherit_from")

            val MANAGER = Relationship("manager")
            val WRITE = Relationship("write")
            val READ = Relationship("read")
        }

        object Permissions {
            val VIEW = Permission("view")
            val EDIT = Permission("edit")
            val MANAGE = Permission("manage")
        }
    }
}

interface RebacRepository {

    fun readRelationships(
        resourceType: Resource,
        resourceId: String,
        relationship: services.oneteam.ai.permissions.rebac.Relationship? = null,
        subjectType: Resource? = null,
        subjectId: String? = null,
        token: String? = null
    ): List<RelationshipModel>

    fun writeRelationship(
        resourceType: Resource, resourceId: String, relationship: Relationship, subjectType: Resource, subjectId: String
    ): String

    fun findAllResourcesWithPermissionForSubject(
        resourceType: Resource,
        permission: Permission,
        subjectType: Resource,
        subjectId: String,
        token: String? = null
    ): List<String>

    fun findAllSubjectsWithPermissionOnResource(
        resourceType: Resource,
        resourceId: String,
        permission: Permission,
        token: String? = null
    ): List<String>

    fun checkSubjectPermissionForResource(
        resourceType: Resource,
        resourceId: String,
        permission: Permission,
        subjectType: Resource,
        subjectId: String,
        token: String? = null
    ): Boolean

    fun checkSubjectRelationshipForResource(
        resourceType: Resource,
        resourceId: String,
        relationship: Relationship,
        subjectType: Resource,
        subjectId: String,
        token: String? = null
    ): Boolean

    fun findAllRelationshipsForUser(
        subjectType: Resource,
        subjectId: String,
        permission: Permission,
        prefix: String? = null,
        token: String? = null
    ): List<String>

    fun deleteRelationshipsForResourceAndSubject(resourceType: Resource, resourceId: String, subjectId: String): String

    fun deleteAllRelationshipsForResource(resourceType: Resource, resourceId: String): String

    fun deleteAllRelationshipsForType(resourceType: Resource, relationship: Relationship): String

    fun writeSchema(schema: String): String

    fun deleteRelationshipForResource(resourceType: Resource, resourceId: String, relationship: Relationship): String

    fun deleteRelationshipForResource(
        resourceType: Resource,
        resourceId: String,
        relationship: Relationship,
        subjectType: Resource,
        subjectId: String
    ): String

}