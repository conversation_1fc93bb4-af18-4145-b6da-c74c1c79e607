{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:best-practices"], "prHeader": "OA-272 ### Renovate Bot Update\n\nThis PR updates the following dependencies:", "branchPrefix": "renovate/OA-272_", "commitMessagePrefix": "OA-272 ", "timezone": "Australia/Sydney", "schedule": "after 4am and before 8am on Monday", "minimumReleaseAge": "14 days", "packageRules": [{"minimumReleaseAge": "14 days", "automerge": true, "groupName": "all non-major dependencies", "groupSlug": "all-minor-patch", "matchPackageNames": ["*", "!node"], "matchUpdateTypes": ["minor", "patch"]}]}