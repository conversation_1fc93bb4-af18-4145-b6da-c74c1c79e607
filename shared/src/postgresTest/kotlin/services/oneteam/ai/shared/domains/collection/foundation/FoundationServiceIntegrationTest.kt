package services.oneteam.ai.shared.domains.collection.foundation

import io.kotest.common.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.TestDatabaseInitializer
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.collection.Visibility
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.helpers.CustomNanoId
import services.oneteam.ai.shared.permissions.PermissionDeniedException
import services.oneteam.ai.shared.withTenantTransactionScope

class FoundationServiceIntegrationTest {
    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val fixtures = TestDatabaseInitializer.fixtures
    val foundationService = fixtures.foundationService
    val workspaceService = fixtures.workspaceService
    val testSupport = FoundationTestSupport(fixtures)

    /*
    * Feature: Foundation management
    *
    * Scenario: Creating a foundation adds the creator as a manager
    *
    * GIVEN I log in as "user1"
    * WHEN I create a workspace
    * AND I create a foundation under the root foundation
    * THEN I should be the only member of that foundation
    * AND I should have "MANAGE" access to that foundation
    * AND "user2" (not a member) should not be able to see the foundation
    * AND "user3" (from another tenant) should not be able to see the foundation
     */
    @Test
    fun `Creating a foundation adds the creator as a manager`() {
        with(testSupport) {
            runBlocking {

                val user1 = fixtures.tenant1User1Session
                val user2 = fixtures.tenant1User2Session
                val user3 = fixtures.tenant2User1Session

                val foundation = fixtures.withTestUser(user1) { user ->
                    // prepare
                    val workspace = workspaceService.get(Fixtures.Workspaces.Minimal.KEY)
                    val rootFoundation = foundationService.root(workspace.id)

                    // perform
                    val foundation = createFoundation(
                        workspace.id,
                        rootFoundation,
                        Fixtures.Workspaces.Minimal.FoundationConfigurations.Company,
                        user1
                    )

                    logger.info("Working with test foundation {}", foundation)

                    /*
                     * verify
                     */

                    assertThat(foundation).isNotNull()

                    withTenantTransactionScope {

                        val members = foundationMembersService.search(
                            FoundationMembersSearchCriteria.byFoundation(foundation.id), PageRequest.forMax()
                        )

                        // there should be one member - the user who created the foundation
                        assertThat(members.items).hasSize(1)
                        assertThat(members.items[0].userId).isEqualTo(user1.userId)
                        // ensure the user creating the foundation is a manager
                        assertThat(members.items[0].accessLevel).isEqualTo(FoundationMembers.AccessLevel.MANAGER)
                        // ensure the foundation can be retrieved by this user
                        assertThat(foundationService.get(foundation.id)).isNotNull
                        // ensure the foundation defaults to INHERIT visibility
                        assertThat(foundationService.get(foundation.id).visibility).isEqualTo(Visibility.INHERIT)
                    }

                    withTenantTransactionScope { tenant ->
                        fixtures.syncTenant.sync(tenant)
                        // after syncing the user should still have visibility
                        assertThat(foundationService.get(foundation.id)).isNotNull
                    }

                    foundation
                }

                userShouldSeeFoundation(user1, foundation)
                userShouldNotSeeFoundation(user2, foundation)
                userShouldNotSeeFoundation(user3, foundation)
            }
        }
    }

    /*
    * Feature: Foundation management
    *
    * Scenario: Visibility of root foundation should be INVITE_ONLY and not changeable
    *
    * GIVEN I log in as "user1"
    * WHEN I create a workspace
    * THEN the root foundation should have "INVITE_ONLY" visibility
    * WHEN I change the visibility of the root foundation to "INHERIT"
    * THEN I should get an error
    * THEN the visibility should still be "INVITE_ONLY"
    */

    @Test
    fun `Visibility of root foundation should be INVITE_ONLY and not changeable`() {
        runBlocking {

            val user1 = fixtures.tenant1User1Session

            fixtures.withTestUser(user1) { user ->

                // prepare
                val workspace = workspaceService.create(
                    Workspace.ForCreate(
                        Workspace.Name("Test workspace"),
                        Workspace.Key("t${CustomNanoId.generate(5)}"),
                    ), user.userId
                )
                val rootFoundation = foundationService.root(workspace.id)

                // verify
                assertThat(rootFoundation.visibility).isEqualTo(Visibility.INVITE_ONLY)

                // perform + verify
                try {
                    foundationService.updateVisibility(
                        rootFoundation.id,
                        Visibility.INHERIT,
                        user.userId
                    )
                    fail { "Should not have been able to change visibility of root foundation" }
                } catch (e: PermissionDeniedException) {
                    // expected
                    logger.info("Expected exception: {}", e.message)
                }

                // verify
                assertThat(foundationService.get(rootFoundation.id).visibility).isEqualTo(Visibility.INVITE_ONLY)
            }
        }
    }


    /*
     * Feature: Foundation management
     *
     * Scenario: A user without permission to the parent cannot create a foundation
     *
     * GIVEN I log in as "user1"
     * WHEN I create a workspace
     * AND I create a foundation under the root foundation
     * AND I add "user2" as a foundation member with "READ" access
     * THEN "user2" should be able to see the foundation
     * AND "user2" should not be able to create a foundation under that foundation
     */
    @Test
    fun `A user without permission to the parent cannot create a foundation`() {
        with(testSupport) {
            runBlocking {

                val user1 = fixtures.tenant1User1Session
                val user2 = fixtures.tenant1User2Session

                val foundationConfigurationId = Fixtures.Workspaces.Minimal.FoundationConfigurations.Company

                /*
                 * prepare
                 */
                // GIVEN "user1" is logged in
                val foundation = fixtures.withTestUser(user1) { user ->

                    // create workspace and root foundation as user1
                    // AND creates the "Minimal" workspace
                    val workspace = workspaceService.get(Fixtures.Workspaces.Minimal.KEY)
                    val rootFoundation = foundationService.root(workspace.id)

                    // create a foundation under root
                    // user1 can do this because they have manage permissions on root
                    // AND creates a foundation under root
                    val foundation = createFoundation(workspace.id, rootFoundation, foundationConfigurationId, user)

                    // add user2 to foundation with READ access
                    // AND adds "user2" as a foundation member with "READ" access
                    addMember(foundation, user2, FoundationMembers.AccessLevel.READ)

                    return@withTestUser foundation
                }

                userShouldSeeFoundation(user2, foundation)

                fixtures.withTestUser(user2) { user ->
                    // perform + verify
                    try {
                        val foundation =
                            createFoundation(foundation.workspaceId, foundation, foundationConfigurationId, user)
                        fail { "User ${user.userId} should not have been able to create a foundation under ${foundation.id} when only having READ access level on parent" }
                    } catch (e: PermissionDeniedException) {
                        // expected
                        logger.info("Expected exception: ${e.message}")
                    }
                }
            }
        }
    }

}
