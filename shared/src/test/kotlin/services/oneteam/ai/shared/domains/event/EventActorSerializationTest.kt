package services.oneteam.ai.shared.domains.event

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace

class EventActorSerializationTest {

    data class SerializationSpec(val input: EventActor, val expected: String)

    @TestInstance(TestInstance.Lifecycle.PER_CLASS)
    companion object {
        @JvmStatic
        fun scenarios() = listOf(
            SerializationSpec(EventActor.UNKNOWN, "unknown"),
            SerializationSpec(EventActor.SYSTEM, "system"),
            SerializationSpec(EventActor.User(123L), "123"),
            SerializationSpec(
                EventActor.Flow(
                    workspaceId = Workspace.Id(123),
                    flowConfigurationId = FlowConfiguration.Id("fc_456"),
                    stepId = FlowConfiguration.Step.Id("step_789"),
                    flowExecutionId = "fe_101112"
                ),
                "123:fc_456:step_789:fe_101112"
            )
        )
    }

    @ParameterizedTest
    @MethodSource("scenarios")
    fun testSerialization(spec: SerializationSpec) {
        assertThat(spec.input.serializeToString()).isEqualTo(spec.expected)
        assertThat(EventActor.deserializeFromString(spec.expected)).isEqualTo(spec.input)
    }
}
