package services.oneteam.ai.shared

import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withContext
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.Fixtures.Companion.anyTenant
import services.oneteam.ai.shared.Fixtures.Companion.anyUserSession
import services.oneteam.ai.shared.domains.auth.SystemUserPrincipal
import services.oneteam.ai.shared.middlewares.RequestContext

class WithSystemUserTest {
    @Test
    fun `withSystemUser sets SystemUserPrincipal and preserves tenant`() = runTest {
        val tenant = anyTenant()
        val principal = anyUserSession(tenant)

        withContext(RequestContext(tenant, principal)) {

            withSystemUser {
                // inside withSystemUser, should have SystemUserPrincipal and same tenant
                val ctx = currentCoroutineContext()[RequestContext]
                assertThat(ctx).isNotNull
                assertThat(ctx!!.tenant).isEqualTo(tenant)
                assertThat(ctx.principal).isInstanceOf(SystemUserPrincipal::class.java)
                "ok"
            }

            // should find original principal and tenant now we are out of withSystemUser
            assertThat(currentCoroutineContext()[RequestContext]!!.principal).isEqualTo(principal)
            assertThat(currentCoroutineContext()[RequestContext]!!.tenant).isEqualTo(tenant)

        }
    }


}
