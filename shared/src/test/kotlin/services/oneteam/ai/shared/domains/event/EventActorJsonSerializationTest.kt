package services.oneteam.ai.shared.domains.event

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import services.oneteam.ai.shared.otSerializer

class EventActorJsonSerializationTest {

    @Test
    fun `should serialize and deserialize EventActor`() {
        val originalActor = EventActor.UNKNOWN

        val serialized = otSerializer.encodeToString<EventActor>(originalActor)
        val deserialized = otSerializer.decodeFromString<EventActor>(serialized)

        assertEquals(originalActor, deserialized)
    }
}