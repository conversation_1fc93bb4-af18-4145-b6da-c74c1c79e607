package services.oneteam.ai.shared.domains.actions

import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class OTStorageServiceTest {
    data class Spec(
        val tenantName: String,
        val filePath: String,
        val expected: String
    )

    fun filePathProvider(): List<Spec> {
        return listOf(
            Spec(
                tenantName = "ot",
                filePath = "path/to file.txt",
                expected = "https://myStorage.blob.core.windows.net/myContainer/path/to%20file.txt?mySasToken=123&sig=%2B5034Nk"
            ),
            Spec(
                tenantName = "ot",
                filePath = "another/path/to-file.json",
                expected = "https://myStorage.blob.core.windows.net/myContainer/another/path/to-file.json?mySasToken=123&sig=%2B5034Nk"
            ),
            Spec(
                tenantName = "other",
                filePath = "another/path/to-file.json",
                expected = "https://otherStorage.blob.core.windows.net/myContainer/another/path/to-file.json?otherSasToken=321&sig=%2B5034Nk"
            )
        )
    }

    @ParameterizedTest
    @MethodSource("filePathProvider")
    fun `generates valid fileUrl`(spec: Spec) = runTest {
        val service = OTStorageService(
            OTStorageService.OTStorageServiceConfig(
                byTenant = mapOf(
                    "ot" to OTStorageService.OTStorageServiceConfig.Config(
                        storageName = "myStorage",
                        containerName = "myContainer",
                        sasToken = "mySasToken=123&sig=%2B5034Nk"
                    ),
                    "other" to OTStorageService.OTStorageServiceConfig.Config(
                        storageName = "otherStorage",
                        containerName = "myContainer",
                        sasToken = "otherSasToken=321&sig=%2B5034Nk"
                    )
                )
            )
        )
        val output = service.getFileUrl(spec.tenantName, spec.filePath)
        assert(output == spec.expected) {
            "Expected: ${spec.expected}, but got: $output"
        }
    }

    fun `expects to throw if missing config for tenant`() = runTest {
        val service = OTStorageService(
            OTStorageService.OTStorageServiceConfig(
                byTenant = mapOf(
                    "ot" to OTStorageService.OTStorageServiceConfig.Config(
                        storageName = "myStorage",
                        containerName = "myContainer",
                        sasToken = "mySasToken=123&sig=%2B5034Nk"
                    )
                )
            )
        )
        try {
            service.getFileUrl("unknown", "path/to/file.txt")
            assert(false) { "Expected exception to be thrown" }
        } catch (e: IllegalArgumentException) {
            assert(e.message == "No OneTeam storage config found for tenant: unknown") {
                "Unexpected exception message: ${e.message}"
            }
        }
    }
}