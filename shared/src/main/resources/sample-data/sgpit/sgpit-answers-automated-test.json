{"answers": {"aC2mcecXgh": {"questionId": "aC2mcecXgh", "type": "table", "value": {"entities": {"aWFOdcvtHHInShF17dGtq": {"columns": {"aashs2eBrn": {"questionId": "aashs2eBrn", "type": "text", "value": "[{\"number_1\":123,\"text_1\":\"qwe\"}]"}, "arRas3fgoi": {"questionId": "arRas3fgoi", "type": "text", "value": "Send API Request list schema"}}, "id": "aWFOdcvtHHInShF17dGtq"}, "aXZKHwEh3ycKE8kF5oRjY": {"columns": {"aashs2eBrn": {"questionId": "aashs2eBrn", "type": "text", "value": "{\"author\":\"local\",\"branch\":\"main\",\"date\":\"now\",\"hash\":\"0\"}"}, "arRas3fgoi": {"questionId": "arRas3fgoi", "type": "text", "value": "Send API Request json schema"}}, "id": "aXZKHwEh3ycKE8kF5oRjY"}, "aYRCsSzt0Gby8032GQEKx": {"columns": {"aashs2eBrn": {"questionId": "aashs2eBrn", "type": "text", "value": "{\"form\":{\"id\":297},\"nested\":{\"test\":true},\"number_1\":123,\"text_1\":\"qwe\"}"}, "arRas3fgoi": {"questionId": "arRas3fgoi", "type": "text", "value": "Receive Webhook json schema"}}, "id": "aYRCsSzt0Gby8032GQEKx"}, "ahlpCCqBwJqADzNhqqwb0": {"columns": {"aashs2eBrn": {"questionId": "aashs2eBrn", "type": "text", "value": "{\"boolean_1\":true,\"form\":{\"id\":297},\"nested\":{\"shouldBeFiltered\":\"abc\",\"test\":true},\"number_1\":123,\"text_1\":\"qwe\"}"}, "arRas3fgoi": {"questionId": "arRas3fgoi", "type": "text", "value": "Receive Webhook no schema"}}, "id": "ahlpCCqBwJqADzNhqqwb0"}, "arnGjPkzqcmDt11wwytOA": {"columns": {"aashs2eBrn": {"questionId": "aashs2eBrn", "type": "text", "value": "{\"version\":\"local\",\"branch\":\"main\",\"hash\":\"0\",\"date\":\"now\",\"author\":\"local\"}"}, "arRas3fgoi": {"questionId": "arRas3fgoi", "type": "text", "value": "Send API Request no schema"}}, "id": "arnGjPkzqcmDt11wwytOA"}, "axodzEdoh9kEqa6lK92Jx": {"columns": {"aashs2eBrn": {"questionId": "aashs2eBrn", "type": "text", "value": "[{\"number_1\":123,\"text_1\":\"qwe\",\"form\":{\"id\":297}}]"}, "arRas3fgoi": {"questionId": "arRas3fgoi", "type": "text", "value": "Receive Webhook list schema"}}, "id": "axodzEdoh9kEqa6lK92Jx"}}, "order": ["axodzEdoh9kEqa6lK92Jx", "aYRCsSzt0Gby8032GQEKx", "aWFOdcvtHHInShF17dGtq", "aXZKHwEh3ycKE8kF5oRjY", "ahlpCCqBwJqADzNhqqwb0", "arnGjPkzqcmDt11wwytOA"]}}, "azoPVe79D9": {"questionId": "azoPVe79D9", "type": "table", "value": {"entities": {"a1QMBeptpYGby5R8do1dl": {"columns": {"a45J1OdGOt": {"questionId": "a45J1OdGOt", "type": "text", "value": "{\"author\":\"local\",\"branch\":\"main\",\"date\":\"now\",\"hash\":\"0\"}"}, "a8bOu6zP1U": {"questionId": "a8bOu6zP1U", "type": "text", "value": "{\"branch\":\"main\",\"hash\":\"0\",\"date\":\"now\",\"author\":\"local\"}"}, "aBuAuPBRBo": {"questionId": "aBuAuPBRBo", "type": "text", "value": "PASS"}, "aoWyVeCEMC": {"questionId": "aoWyVeCEMC", "type": "text", "value": "json schema"}}, "id": "a1QMBeptpYGby5R8do1dl"}, "ar112OZNPs5rve3OWz8iA": {"columns": {"a45J1OdGOt": {"questionId": "a45J1OdGOt", "type": "text", "value": "[{\"number_1\":123,\"text_1\":\"qwe\"}]"}, "a8bOu6zP1U": {"questionId": "a8bOu6zP1U", "type": "text", "value": "[{\"number_1\":123,\"text_1\":\"qwe\"}]"}, "aBuAuPBRBo": {"questionId": "aBuAuPBRBo", "type": "text", "value": "PASS"}, "aoWyVeCEMC": {"questionId": "aoWyVeCEMC", "type": "text", "value": "list schema"}}, "id": "ar112OZNPs5rve3OWz8iA"}}, "order": ["ar112OZNPs5rve3OWz8iA", "a1QMBeptpYGby5R8do1dl"]}}}, "id": 297, "workspaceVersionId": 0}