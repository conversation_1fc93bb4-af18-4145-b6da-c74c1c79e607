package services.oneteam.ai.shared.middlewares

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.UserSession
import services.oneteam.ai.shared.domains.event.EventActor
import services.oneteam.ai.shared.domains.tenant.Tenant
import kotlin.coroutines.AbstractCoroutineContextElement
import kotlin.coroutines.CoroutineContext

class RequestContext(
    val tenant: Tenant,
    val principal: Any? = null,
    val actor: EventActor = EventActor.UNKNOWN,
) : AbstractCoroutineContextElement(RequestContext) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    companion object Key : CoroutineContext.Key<RequestContext>

    init {
        logger.info("RequestContext created: $this")
    }

    override fun toString(): String {
        return "RequestContext(principal=$principal,tenant=$tenant,actor=$actor)"
    }

    fun principalId(): Long? {
        if (principal is UserSession) {
            return (principal as UserSession?)?.user?.id
        }
        return null
    }

}