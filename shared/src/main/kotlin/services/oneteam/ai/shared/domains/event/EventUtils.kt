package services.oneteam.ai.shared.domains.event

import io.ktor.http.*
import services.oneteam.ai.shared.common.OTAIHeaders
import services.oneteam.ai.shared.domains.user.User

fun constructActorFromHeaders(headers: Headers, userId: User.Id? = null): EventActor {
    // temporary support for both headers - we will deprecate X_OTAI_FLOW in the future
    val flowActorHeader = headers[OTAIHeaders.X_OTAI_ACTOR] ?: headers[OTAIHeaders.X_OTAI_FLOW]

    if (flowActorHeader?.isNotEmpty() == true) {
        return EventActor.deserializeFromString(flowActorHeader)
    }

    if (userId != null) {
        return EventActor.User(userId.value)
    }

    return EventActor.UNKNOWN
}