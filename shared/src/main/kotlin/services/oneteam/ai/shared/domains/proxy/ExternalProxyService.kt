package services.oneteam.ai.shared.domains.proxy

import io.ktor.client.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.request.*
import io.ktor.client.request.forms.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonPrimitive
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.common.OTAIHeaders
import services.oneteam.ai.shared.domains.event.EventActor
import services.oneteam.ai.shared.middlewares.RequestContext

class ExternalProxyService(
    private val serviceAccountToken: String, private val _client: HttpClient = HttpClient {
        install(ContentNegotiation) { json(); }
        install(HttpTimeout)
    }
) : ProxyService {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    override suspend fun call(
        body: ProxyService.ProxyEndpointBody, actor: EventActor, isExternalResponse: Boolean?, timeoutMillis: Long?
    ): ProxyService.ProxyEndpointResponse {
        try {
            logger.debug("Proxy call to {} has started", body.url)

            val method = getClientMethod(body)
            val block = createBlock(body, actor, timeoutMillis)

            val res = _client.method(body.url, block)

            val isSuccess = res.status.value.toString().substring(0, 1) == "2"
            if (isSuccess) {
                logger.debug("Proxy call to {} was successful", body.url)
            } else {
                logger.error("Proxy call to {} failed with status {}", body.url, res.status.value)
            }

            val bodyAsText = res.bodyAsText().ifEmpty {
                "{}"
            }

            return if (isExternalResponse == true) {
                val externalEndpointResponse = ProxyService.ExternalEndpointResponse(
                    status = res.status.value,
                    payload = Json.parseToJsonElement(bodyAsText),
                    errors = if (!isSuccess) JsonPrimitive(res.bodyAsText()) else JsonNull
                )
                ProxyService.ProxyEndpointResponse(
                    response = Json.encodeToString(externalEndpointResponse),
                    status = if (isSuccess) ProxyService.ProxyEndpointResponseStatus.SUCCESS else ProxyService.ProxyEndpointResponseStatus.FAIL,
                    error = if (!isSuccess) bodyAsText else null
                )
            } else {
                ProxyService.ProxyEndpointResponse(
                    response = bodyAsText,
                    status = if (isSuccess) ProxyService.ProxyEndpointResponseStatus.SUCCESS else ProxyService.ProxyEndpointResponseStatus.FAIL,
                    error = if (!isSuccess) bodyAsText else null
                )
            }
        } catch (e: Exception) {
            logger.error("Proxy call to ${body.url} failed with error: ${e.message}", e)
            return ProxyService.ProxyEndpointResponse(
                response = null, status = ProxyService.ProxyEndpointResponseStatus.FAIL, error = e.message
            )
        }
    }

    private suspend fun createBlock(
        proxyParams: ProxyService.ProxyEndpointBody, actor: EventActor, timeoutMillis: Long?
    ): HttpRequestBuilder.() -> Unit {
        val tenant = currentCoroutineContext()[RequestContext]!!.tenant
        return {
            val resolvedTimeout = if (timeoutMillis != null && timeoutMillis > 0) {
                timeoutMillis
            } else {
                // TODO: make default an environment variable
                60_000L
            }
            timeout {
                requestTimeoutMillis = resolvedTimeout
            }
            header(HttpHeaders.Referrer, tenant.originUrl)

            if (proxyParams.authentication?.get("useOtaiServiceAccount") == "true") {
                header("Authorization", "Bearer $serviceAccountToken")
                header(OTAIHeaders.X_OTAI_FLOW, actor.serializeToString())
                header(OTAIHeaders.X_OTAI_ACTOR, actor.serializeToString())
            }

            // Content-Type in headers will not be used, it will be overridden in setProxyBody
            // See ActionExecutionStep jsonOrFormCall
            proxyParams.headers?.forEach { (key, value) -> header(key, value) }
            proxyParams.authentication?.forEach { (key, value) -> header(key, value) }
            proxyParams.options?.forEach { (key, value) -> header(key, value) }
            proxyParams.queryParams?.forEach { (key, value) -> parameter(key, value) }

            setProxyBody(proxyParams.body)

        }
    }

    private fun HttpRequestBuilder.setProxyBody(body: ProxyService.ProxyRequestBody?) {

        when (body) {
            is ProxyService.ProxyRequestBody.Json -> {
                contentType(ContentType.Application.Json)
                setBody(body.value)
            }

            is ProxyService.ProxyRequestBody.FormUrlEncoded -> {
                contentType(ContentType.Application.FormUrlEncoded)
                setBody(FormDataContent(Parameters.build {
                    body.params.forEach { (k, v) -> append(k, v) }
                }))
            }

            is ProxyService.ProxyRequestBody.Multipart -> {
                contentType(ContentType.MultiPart.FormData)
                setBody(MultiPartFormDataContent(formData {
                    body.fileInfos.forEach { fileInfo ->
                        append("files", fileInfo.fileBytes, Headers.build {
                            append(
                                HttpHeaders.ContentDisposition,
                                "form-data; name=${fileInfo.fileName}; filename=\"${fileInfo.fileName}\""
                            )
                            append(HttpHeaders.ContentType, fileInfo.contentType)
                        })
                    }
                }))
            }

            null -> {
                // No body to set
            }
        }
    }

    private fun getClientMethod(
        body: ProxyService.ProxyEndpointBody
    ): suspend HttpClient.(String, HttpRequestBuilder.() -> Unit) -> HttpResponse {
        return when (body.method) {
            HttpMethod.Get.value -> { urlString, builder -> get(urlString, builder) }
            HttpMethod.Post.value -> { urlString, builder -> post(urlString, builder) }
            HttpMethod.Put.value -> { urlString, builder -> put(urlString, builder) }
            HttpMethod.Delete.value -> { urlString, builder -> delete(urlString, builder) }
            HttpMethod.Patch.value -> { urlString, builder -> patch(urlString, builder) }
            HttpMethod.Head.value -> { urlString, builder -> head(urlString, builder) }
            HttpMethod.Options.value -> { urlString, builder -> options(urlString, builder) }
            else -> throw Exception("Not supported yet")
        }
    }

}
