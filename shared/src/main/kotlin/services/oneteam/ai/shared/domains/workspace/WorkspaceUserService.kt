package services.oneteam.ai.shared.domains.workspace

import services.oneteam.ai.permissions.rebac.PermissionStructure
import services.oneteam.ai.permissions.rebac.ResourceCollection
import services.oneteam.ai.shared.domains.BadRequestException
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.permissions.PermissionsService
import services.oneteam.ai.shared.permissions.WorkspacePermissionService
import services.oneteam.ai.shared.permissions.WorkspaceRelationshipBuilder
import services.oneteam.ai.shared.withCurrentUser
import services.oneteam.ai.shared.withTenantTransactionScope

class WorkspaceUserService(
    private val workspaceUserRepository: WorkspaceUserRepository,
    private val workspacePermissionService: WorkspacePermissionService,
    private val permissionsService: PermissionsService,
) {

    /**
     * Update permissions in SpiceDB and sync to local index for all resources in the foundation's workspace
     * for affected members.
     */
    private suspend fun doPermissionsUpdate(
        workspace: Workspace.Id,
        members: List<WorkspaceUser.ForApi>
    ) {
        withTenantTransactionScope { tenant ->
            // update spicedb and sync to local index
            permissionsService.update(
                tenant.id,
                WorkspaceRelationshipBuilder.buildMembers(
                    workspace,
                    WorkspaceRelationshipBuilder.buildRelationshipsFromMembers(members)
                ),
                listOf(
                    ResourceCollection(
                        resourceType = PermissionStructure.Workspace.NAME,
                        ids = setOf(workspace.value)
                    )
                )
            )

        }
    }

    suspend fun search(
        pageRequest: PageRequest, workspaceUserSearchCriteria: WorkspaceUserSearchCriteria
    ): Page<WorkspaceUser.ForApi> = withTenantTransactionScope {
        workspaceUserRepository.searchByCriteria(pageRequest, workspaceUserSearchCriteria)
    }


    /**
     * We can only do this if the user making this request has SETTINGS access to the workspace.
     */
    suspend fun create(
        workspaceId: Workspace.Id,
        workspaceUser: WorkspaceUser.ForCreate
    ): WorkspaceUser.ForApi =
        withCurrentUser { currentUser ->
            withTenantTransactionScope { tenant ->
                /*
                 * Check if the user creating this workspace user can manage workspace users
                 */
                workspacePermissionService.canManageMembers(workspaceId, currentUser.userId).assertAllowed()

                workspaceUserRepository.create(tenant, workspaceId, workspaceUser)
                    .also {
                        doPermissionsUpdate(workspaceId, listOf(it))
                    }
            }

        }

    /**
     * We can only do this if the user making this request has SETTINGS access to the workspace.
     */
    suspend fun update(
        workspaceId: Workspace.Id,
        workspaceUser: WorkspaceUser.ForUpdate
    ): WorkspaceUser.ForApi =
        withCurrentUser { currentUser ->
            withTenantTransactionScope {
                /*
                 * Check if the user creating this workspace user can manage workspace users
                 */
                workspacePermissionService.canManageMembers(workspaceId, currentUser.userId).assertAllowed()

                val existingMember =
                    workspaceUserRepository.findByWorkspaceAndUserId(workspaceId, workspaceUser.userId)
                        ?: throw BadRequestException("Member with userId ${workspaceUser.userId.value} not found for workspace ${workspaceId.value}")

                workspaceUserRepository.update(
                    existingMember.id,
                    workspaceUser.status,
                    workspaceUser.accessLevel
                ).also {
                    doPermissionsUpdate(workspaceId, listOf(it))
                }
            }
        }

    suspend fun findByWorkspaceAndUserId(
        workspaceId: Workspace.Id,
        userId: User.Id
    ): WorkspaceUser.ForApi? =
        withTenantTransactionScope {
            workspaceUserRepository.findByWorkspaceAndUserId(workspaceId, userId)
        }

    /**
     * If any member is not in the workspace users they need to be added to workspace users table with accessLevels provided
     */
    suspend fun upsertMember(
        workspaceId: Workspace.Id,
        userId: User.Id,
        accessLevels: List<WorkspaceUser.AccessLevel>
    ) = withTenantTransactionScope {

        search(
            PageRequest.forOne(),
            WorkspaceUserSearchCriteria.byWorkspaceAndUser(workspaceId, userId)
        ).items.firstOrNull()
            ?.let {

                // If any member is in the workspace users as status=inactive TBD - for now throw an error
                if (it.status == WorkspaceUser.Status.INACTIVE) {
                    throw BadRequestException("User $userId is inactive in workspace $workspaceId so cannot be re-added to workspace")
                }

                // If they are already a workspace user then we need to add the given access levels to their accessLevels if not already present
                update(
                    workspaceId,
                    WorkspaceUser.ForUpdate(
                        userId = it.user.id,
                        accessLevel = (it.accessLevel + accessLevels).distinct(),
                        status = it.status
                    )
                )
            }
            ?: create(
                workspaceId,
                WorkspaceUser.ForCreate(
                    userId = userId,
                    accessLevel = accessLevels,
                    status = WorkspaceUser.Status.ACTIVE
                )
            )
    }

    /**
     * Check if a user is a member of a workspace with the given access level.
     */
    suspend fun isMember(workspaceId: Workspace.Id, userId: User.Id, accessLevel: WorkspaceUser.AccessLevel): Boolean =
        withTenantTransactionScope {
            search(
                PageRequest.forOne(),
                WorkspaceUserSearchCriteria.byWorkspaceAndUser(workspaceId, userId)
            ).items.firstOrNull()?.accessLevel?.contains(accessLevel) == true
        }
}

