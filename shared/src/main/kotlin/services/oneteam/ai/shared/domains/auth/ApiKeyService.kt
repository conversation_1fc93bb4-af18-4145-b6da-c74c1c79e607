package services.oneteam.ai.shared.domains.auth

import kotlinx.serialization.Serializable
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.passvault.KeyVaultService
import services.oneteam.ai.shared.domains.passvault.PassVault
import services.oneteam.ai.shared.domains.passvault.PassVaultHasher
import services.oneteam.ai.shared.helpers.CustomNanoId
import services.oneteam.ai.shared.withTenantTransactionScope

@Serializable
class ApiKeyPrincipal(val name: String)


const val API_KEY_DELIMITER = "."

class ApiKeyService(
    val keyVaultService: KeyVaultService,
    val apiKeyRepository: ApiKeyRepository
) {
    private fun getPassVaultHasher(): PassVaultHasher {
        return PassVault(keyVaultService).getHasher()
    }

    private fun composeApiKey(clientId: String, clientSecret: String): String {
        return listOf(clientId, clientSecret).joinToString(separator = API_KEY_DELIMITER)
    }

    private fun decomposeApiKey(apiKey: ApiKey.RevealedApiKey): Pair<String, String> {
        val parts = apiKey.value.split(API_KEY_DELIMITER)
        require(parts.size == 2) { "Invalid API key format" }
        return Pair(parts[0], parts[1])
    }

    private fun generateApiKey(): Triple<String, String, ApiKey.RevealedApiKey> {
        val clientId = CustomNanoId.generate(16)
        val clientSecret = CustomNanoId.generate(32)
        return Triple(
            clientId,
            clientSecret,
            ApiKey.RevealedApiKey(composeApiKey(clientId, clientSecret))
        )
    }

    suspend fun create(
        createParams: ApiKey.ForCreate
    ): ApiKey.RevealedAfterCreate = withTenantTransactionScope { tenant ->
        val (clientId, clientSecret, revealedApiKey) = generateApiKey()
        val hashedClientSecret = ApiKey.HashedClientSecret(getPassVaultHasher().hash(clientSecret))
        val apiKeyEntity = apiKeyRepository.create(tenant, createParams, clientId, hashedClientSecret)

        val apiKeyAfterCreate = ApiKey.RevealedAfterCreate(
            id = apiKeyEntity.id,
            name = apiKeyEntity.name,
            description = apiKeyEntity.description,
            scopes = apiKeyEntity.scopes,
            apiKey = revealedApiKey,
            metadata = apiKeyEntity.metadata
        )

        return@withTenantTransactionScope apiKeyAfterCreate
    }

    suspend fun search(
        pageRequest: PageRequest, searchCriteria: ApiKeySearchCriteria
    ): Page<ApiKey.ForApi> = withTenantTransactionScope {
        val searchResult = apiKeyRepository.search(
            pageRequest, searchCriteria
        )
        return@withTenantTransactionScope searchResult
    }

    suspend fun delete(
        apiKeyId: ApiKey.Id
    ) = withTenantTransactionScope {
        apiKeyRepository.delete(apiKeyId)
    }

    suspend fun login(
        apiKey: ApiKey.RevealedApiKey
    ): ApiKeyPrincipal? = withTenantTransactionScope {
        val (clientId, clientSecret) = decomposeApiKey(apiKey)
        val maybeApiKey = apiKeyRepository.getForLogin(clientId)

        if (maybeApiKey == null) {
            return@withTenantTransactionScope null
        }

        val passVault = getPassVaultHasher()
        val isValid = passVault.compareWithHash(clientSecret, maybeApiKey.clientSecret.value)
        if (!isValid) {
            return@withTenantTransactionScope null
        }

        return@withTenantTransactionScope ApiKeyPrincipal("apikey-${maybeApiKey.id}")
    }
}
