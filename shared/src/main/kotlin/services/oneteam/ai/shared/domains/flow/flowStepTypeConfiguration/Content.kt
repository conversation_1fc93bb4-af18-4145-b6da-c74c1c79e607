package services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.KSerializer
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.descriptors.buildClassSerialDescriptor
import kotlinx.serialization.descriptors.element
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.*
import services.oneteam.ai.shared.domains.VariableDataType

//fyi for future devs.
// do not add properties to base (abstract or sealed) classes,
// unless they are specifically used across that hierarchy.
//
// add functions related to the corresponding content type to the specific content class. OOP <3


@OptIn(ExperimentalSerializationApi::class)
@Serializable
@JsonClassDiscriminator("type")
sealed class Content {
    abstract fun getType(): VariableDataType
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Content

        if (description != other.description) return false
        if (identifier != other.identifier) return false
        if (properties != other.properties) return false

        return true
    }

    override fun hashCode(): Int {
        var result = description.hashCode()
        result = 31 * result + identifier.hashCode()
        result = 31 * result + properties.hashCode()
        return result
    }

    var description: String = ""
    var identifier: String = ""
    var properties: Properties = Properties()


}

@Serializable
sealed class QuestionContent(var text: String = "") : Content() {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        if (!super.equals(other)) return false

        other as QuestionContent

        return text == other.text
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + text.hashCode()
        return result
    }
}

@Serializable
@SerialName("section")
class SectionContent() : Content() {
    override fun getType() = VariableDataType.SECTION

    var name: String = ""
    var content: List<Content> = emptyList()
}

@Serializable
@SerialName("select")
class SelectContent : QuestionContent() {
    override fun getType() = VariableDataType.SELECT
}

@Serializable
@SerialName("text")
class TextContent : QuestionContent() {
    override fun getType() = VariableDataType.TEXT
}

@Serializable
@SerialName("date")
class DateContent : QuestionContent() {
    override fun getType() = VariableDataType.DATE
}

@Serializable
@SerialName("number")
class NumberContent : QuestionContent() {
    override fun getType() = VariableDataType.NUMBER
}

@Serializable
@SerialName("boolean")
class BooleanContent : QuestionContent() {
    override fun getType() = VariableDataType.BOOLEAN
}

@Serializable
@SerialName("list")
class ListContent : QuestionContent() {
    override fun getType() = VariableDataType.LIST
}

@Serializable
@SerialName("multiSelect")
class MultiSelectContent : QuestionContent() {
    override fun getType() = VariableDataType.MULTISELECT
}

@Serializable
@SerialName("table")
class TableContent : QuestionContent() {
    override fun getType() = VariableDataType.TABLE
}


@Serializable
@SerialName("variable")
class VariableContent : QuestionContent() {
    override fun getType() = VariableDataType.VARIABLE
}


@Serializable
@SerialName("schema")
class SchemaContent : QuestionContent() {
    override fun getType() = VariableDataType.SCHEMA
}


@Serializable(with = CustomContentTypeSerializer::class)
class CustomTypeContent(
    var rawType: String = ""
) : QuestionContent() {
    override fun getType() = VariableDataType.CUSTOM(rawType)

}


//next on the tech debt chopping block
//subdivide the properties class similar to the content classes.
//IMPORTANT. when this is done, please create A CustomProperties class which houses the typedef
// this will allow us to remove the custom serializer
//then getType() can return this.properties.type
@Serializable
data class Properties(
    val type: String? = null,
    val required: JsonElement? = null, // boolean or Condition
    val hidden: JsonElement? = null, // boolean or Condition
    val disabled: JsonElement? = null, // boolean or Condition
    val regex: String? = null,
    val options: List<Option>? = null,
    val dynamicOptions: DynamicOptions? = null,
    val properties: Properties? = null,
    val defaultValue: JsonElement? = null,
    val columns: List<Content>? = null,
    val description: String? = null,
    val uiControls: UIControls? = null,
)

@Serializable
data class Option(
    val label: String, val value: String, val description: String? = null,
)

@Serializable
data class UIControls(
    val displayAs: String? = null,
    val visual: String? = null,
)

@Serializable
data class DynamicOptions(
    val tag: String? = null, val body: JsonElement? = null
)

//this is a hack to be able to serialize custom types
//the use of this serializer could be avoided by moving the type definition to the Properties class.
//without some migration tool this is too difficult to do right now.
//when properties are reworked this can be removed.
class CustomContentTypeSerializer : KSerializer<CustomTypeContent> {

    override val descriptor: SerialDescriptor = buildClassSerialDescriptor("CustomTypeContent") {
        element<String>("description")
        element<String>("identifier")
        element<Properties>("properties")
        element<String>("text")
    }

    override fun deserialize(decoder: Decoder): CustomTypeContent {
        //hack. I hope this is all removed soon.
        decoder as JsonDecoder

        val elem = decoder.decodeJsonElement();
        val obj = elem.jsonObject;

        val typeFromJson = obj["type"]?.jsonPrimitive?.content ?: "";
        val description = obj["description"]?.jsonPrimitive?.content ?: "";
        val identifier = obj["identifier"]?.jsonPrimitive?.content ?: "";
        val text = obj["text"]?.jsonPrimitive?.content ?: "";
        val properties = obj["properties"]?.let {
            decoder.json.decodeFromJsonElement(Properties.serializer(), it);
        } ?: Properties();

        val inst = CustomTypeContent();

        inst.apply {
            this.rawType = typeFromJson
            this.description = description;
            this.identifier = identifier;
            this.properties = properties;
            this.text = text;
        }

        return inst;
    }

    override fun serialize(encoder: Encoder, value: CustomTypeContent) {
        encoder as JsonEncoder

        val obj = buildJsonObject {
            put("description", JsonPrimitive(value.description));
            put("identifier", JsonPrimitive(value.identifier));
            put("properties", encoder.json.encodeToJsonElement(Properties.serializer(), value.properties));
            put("text", JsonPrimitive(value.text));
            put("type", JsonPrimitive(value.rawType));
        };

        encoder.encodeJsonElement(obj);
    }
}


