package services.oneteam.ai.shared.domains.event

import kotlinx.serialization.*
import kotlinx.serialization.json.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace

@Serializable
enum class EventStatus {
    @SerialName("queued")
    QUEUED,

    @SerialName("processed")
    PROCESSED,
}

val manualKeys = setOf(EventKey.START_FLOW_MANUALLY_FROM_FORM, EventKey.START_FLOW_MANUALLY_FROM_FOUNDATION)

@Serializable
enum class EventKey(val serializer: DeserializationStrategy<Event.EventProperties>) {
    @SerialName("START_flow_manually_from_form")
    START_FLOW_MANUALLY_FROM_FORM(Event.EventProperties.StartFlowManuallyFromFormProperties.serializer()),

    @SerialName("START_flow_manually_from_foundation")
    START_FLOW_MANUALLY_FROM_FOUNDATION(Event.EventProperties.StartFlowManuallyFromFoundationProperties.serializer()),

    @SerialName("CREATE_collection_foundation")
    CREATE_COLLECTION_FOUNDATION(Event.EventProperties.CreateCollectionFoundationProperties.serializer()),

    @SerialName("UPDATE_collection_foundation")
    UPDATE_COLLECTION_FOUNDATION(Event.EventProperties.UpdateCollectionFoundationProperties.serializer()),

    @SerialName("DELETE_collection_foundation")
    DELETE_COLLECTION_FOUNDATION(Event.EventProperties.DeleteCollectionFoundationProperties.serializer()),

    @SerialName("CREATE_collection_form")
    CREATE_COLLECTION_FORM(Event.EventProperties.CreateCollectionFormProperties.serializer()),

    @SerialName("UPDATE_collection_form")
    UPDATE_COLLECTION_FORM(Event.EventProperties.UpdateCollectionFormProperties.serializer()),

    @SerialName("DELETE_collection_form")
    DELETE_COLLECTION_FORM(Event.EventProperties.DeleteCollectionFormProperties.serializer()),

    @SerialName("UPDATE_collection_form_answer")
    UPDATE_COLLECTION_FORM_ANSWER(Event.EventProperties.UpdateCollectionFormAnswerProperties.serializer()),

    @SerialName("RECEIVE_incoming_webhook_flow")
    RECEIVE_INCOMING_WEBHOOK_FLOW(Event.EventProperties.ReceiveIncomingWebhookForFlow.serializer()),

    @SerialName("RECEIVE_incoming_webhook_workspace")
    RECEIVE_INCOMING_WEBHOOK_WORKSPACE(Event.EventProperties.ReceiveIncomingWebhookForWorkspace.serializer()),
}

@OptIn(ExperimentalSerializationApi::class)
@Serializable
@JsonClassDiscriminator("type")
sealed class EventActor {

    abstract fun serializeToString(): String

    companion object {

        val logger: Logger = LoggerFactory.getLogger(EventActor::class.java)

        val UNKNOWN = Unknown("unknown")
        val SYSTEM = System("system")

        fun deserializeFromString(value: String): EventActor {
            try {
                return when (value) {
                    UNKNOWN.id -> UNKNOWN
                    SYSTEM.id -> SYSTEM
                    else -> {
                        if (Flow.matches(value)) {
                            return Flow.deserializeFromString(value)
                        } else User.deserializeFromString(value)
                    }
                }
            } catch (e: Exception) {
                logger.error("Error deserializing actor with value `$value`", e)
                return UNKNOWN
            }
        }

    }

    fun userId(): Long? {
        return when (this) {
            is User -> this.userId
            else -> null
        }
    }

    @Serializable
    @SerialName("unknown")
    data class Unknown(val id: String) : EventActor() {
        override fun serializeToString(): String {
            return id
        }
    }

    @Serializable
    @SerialName("system")
    data class System(val id: String) : EventActor() {
        override fun serializeToString(): String {
            return id
        }
    }

    @Serializable
    @SerialName("user")
    data class User(
        val userId: Long
    ) : EventActor() {
        override fun serializeToString(): String {
            return userId.toString()
        }

        companion object {
            fun deserializeFromString(value: String): User {
                val id = value.toLongOrNull()
                    ?: throw IllegalArgumentException("Invalid user id: $value")
                return User(id)
            }
        }
    }

    @Serializable
    @SerialName("flow")
    data class Flow(
        val workspaceId: Workspace.Id,
        val flowConfigurationId: FlowConfiguration.Id,
        val flowExecutionId: String,
        val stepId: FlowConfiguration.Step.Id? = null,
    ) : EventActor() {

        fun withStepId(stepId: FlowConfiguration.Step.Id): Flow {
            return this.copy(stepId = stepId)
        }

        override fun serializeToString(): String {
            return "${workspaceId.value}:${flowConfigurationId.value}:${stepId?.value ?: ""}:$flowExecutionId"
        }

        companion object {

            fun matches(value: String): Boolean {
                return value.count { it == ':' } == 3
            }

            fun deserializeFromString(value: String): Flow {
                val parts = value.split(":")
                return Flow(
                    flowExecutionId = parts[3],
                    workspaceId = Workspace.Id(parts[0].toLong()),
                    flowConfigurationId = FlowConfiguration.Id(parts[1]),
                    stepId = if (parts[2].isNotEmpty()) FlowConfiguration.Step.Id(parts[2]) else null
                )
            }
        }
    }

    fun toFlowWithStepId(stepId: FlowConfiguration.Step.Id): Flow {
        return when (this) {
            is Flow -> this.withStepId(stepId)
            else -> throw IllegalStateException("Cannot set stepId on non-Flow actor $this")
        }
    }
}


@Serializable
sealed class Event {
    abstract val workspaceId: Workspace.Id

    @Serializable
    @JvmInline
    value class Id(val value: String)

    abstract val eventProperties: EventProperties

    @Serializable(EventPropertiesSerializer::class)
    @OptIn(ExperimentalSerializationApi::class)
    sealed class EventProperties {
        abstract val key: EventKey

        @Polymorphic
        abstract val actor: EventActor

        @Serializable
        data class StartFlowManuallyFromFormProperties(
            val buttonLabel: String?,
            val form: FormMinimal?,
            override val actor: EventActor
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.START_FLOW_MANUALLY_FROM_FORM
        }

        @Serializable
        data class StartFlowManuallyFromFoundationProperties(
            val buttonLabel: String?,
            val foundation: FoundationMinimal?,
            override val actor: EventActor
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.START_FLOW_MANUALLY_FROM_FOUNDATION
        }

        @Serializable
        data class CreateCollectionFoundationProperties(
            val foundation: FoundationMinimal?,
            override val actor: EventActor
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.CREATE_COLLECTION_FOUNDATION
        }

        @Serializable
        data class UpdateCollectionFoundationProperties(
            val foundation: FoundationMinimal?,
            override val actor: EventActor
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.UPDATE_COLLECTION_FOUNDATION
        }

        @Serializable
        data class DeleteCollectionFoundationProperties(
            val foundation: FoundationMinimal?,
            override val actor: EventActor
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.DELETE_COLLECTION_FOUNDATION
        }

        @Serializable
        data class CreateCollectionFormProperties(
            val form: FormMinimal?,
            override val actor: EventActor
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.CREATE_COLLECTION_FORM
        }

        @Serializable
        data class UpdateCollectionFormProperties(
            val form: FormMinimal?,
            override val actor: EventActor
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.UPDATE_COLLECTION_FORM
        }

        @Serializable
        data class DeleteCollectionFormProperties(
            val form: FormMinimal?,
            override val actor: EventActor
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.DELETE_COLLECTION_FORM
        }

        @Serializable
        data class UpdateCollectionFormAnswerProperties(
            val form: FormMinimal?,
            val changes: Changes? = null,
            override val actor: EventActor
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.UPDATE_COLLECTION_FORM_ANSWER

            @Serializable
            data class Changes(
                val questionIds: List<String> = emptyList(),
            )
        }

        @Serializable
        data class ReceiveIncomingWebhookForFlow(
            val payload: JsonElement,
            val flowConfigurationId: String? = null,
            override val actor: EventActor
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.RECEIVE_INCOMING_WEBHOOK_FLOW
        }

        @Serializable
        data class ReceiveIncomingWebhookForWorkspace(
            val payload: JsonElement,
            val triggerIdentifier: String? = null,
            override val actor: EventActor
        ) : EventProperties() {
            @EncodeDefault
            override val key: EventKey = EventKey.RECEIVE_INCOMING_WEBHOOK_WORKSPACE
        }
    }


    @Serializable
    data class ForCreate(
        override val workspaceId: Workspace.Id,
        override val eventProperties: EventProperties,
        val eventGroupId: String? = null
    ) : Event()

    @Serializable
    data class ForApi(
        override val workspaceId: Workspace.Id,
        override val eventProperties: EventProperties,
        val id: Id,
        val tenantId: Long,
        val eventGroupId: String? = null,
        val shouldNotifyUser: Boolean = true,
    ) : Event() {
        fun toMap(): JsonElement {
            // is this good enough? or does it need customisation?
            return Json.encodeToJsonElement(this)
        }
    }

    @Serializable
    data class ForJson(
        override val workspaceId: Workspace.Id,
        override val eventProperties: EventProperties,
        val id: Id,
        val status: EventStatus,
        val tenantId: Long,
        val entityMetadata: EntityMetadata,
        val eventGroupId: String? = null,
        var shouldNotifyUser: Boolean = true
    ) : Event()

    @Serializable
    data class FormMinimal(
        val id: Long,
        val foundationId: Long,
        val foundation: FoundationMinimal? = null,
        val formConfiguration: FormConfigurationMinimal,
        val documentId: String?,
        val intervalId: String?,
    )

    @Serializable
    data class FormConfigurationMinimal(
        val id: String,
        val key: String,
        val name: String,
        val seriesId: String?,
    )


    @Serializable
    data class FoundationMinimal(
        val id: Foundation.Id,
        val name: Foundation.Name,
        val key: Foundation.Key,
        val foundationConfiguration: FoundationConfigurationMinimal,
        val parentId: Foundation.Id?,
    )


    @Serializable
    data class FoundationConfigurationMinimal(
        val id: FoundationConfiguration.Id,
        val name: FoundationConfiguration.Name,
        val description: FoundationConfiguration.Description?,
        val relationship: FoundationConfiguration.Relationship,
        val parentId: FoundationConfiguration.Id?,
        val disableAutoSuggestKey: Boolean? = false
    )

    @Serializable
    data class Question(
        val id: String,
        val type: String,
        val text: String,
        val identifier: String,
        val properties: JsonElement?, // unstructured data
        val answer: JsonElement?,
    )
}


object EventPropertiesSerializer :
    JsonContentPolymorphicSerializer<Event.EventProperties>(Event.EventProperties::class) {
    override fun selectDeserializer(element: JsonElement): DeserializationStrategy<Event.EventProperties> {
        val keyStr = element.jsonObject["key"]?.jsonPrimitive?.contentOrNull
            ?: throw SerializationException("Missing 'key' field")

        val key = getEnumFromSerialName(keyStr, EventKey::class.java)
        return key?.serializer ?: throw SerializationException("Unknown key: $keyStr")
    }
}


fun <T : Enum<T>> getEnumFromSerialName(value: String, enumClass: Class<T>): T? {
    return enumClass.enumConstants.firstOrNull { enumConstant ->
        val serialName = enumClass.getField(enumConstant.name).getAnnotation(SerialName::class.java)?.value
        serialName == value
    }
}