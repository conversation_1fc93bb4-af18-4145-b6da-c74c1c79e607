package services.oneteam.ai.shared.domains.actions

import kotlinx.serialization.Serializable
import java.net.URI

class OTStorageService(
    private val storageConfig: OTStorageServiceConfig
) {
    @Serializable
    data class OTStorageServiceConfig(
        val byTenant: Map<String, Config>,
    ) {
        @Serializable
        data class Config(
            val storageName: String,
            val containerName: String,
            val sasToken: String
        )

        fun getConfigForTenant(tenantName: String): Config {
            return byTenant[tenantName]
                ?: throw IllegalArgumentException("No OneTeam storage config found for tenant: $tenantName")
        }
    }

    /**
     * Generates a URL to access a file in the OT storage service.
     *
     * @param filePath The path to the file within the storage container.
     * @return A URL string that can be used to access the file.
     */
    fun getFileUrl(tenantName: String, filePath: String): String {
        // convoluted but this is the way to get proper URL encoding for the file path
        // we can't pass in the sasToken cos otherwise it also get encoded
        val config = storageConfig.getConfigForTenant(tenantName)
        val fileUrl = URI(
            "https",
            "${config.storageName}.blob.core.windows.net",
            "/${config.containerName}/$filePath",
            null
        ).toString() + "?${config.sasToken}"
        return fileUrl
    }
}
