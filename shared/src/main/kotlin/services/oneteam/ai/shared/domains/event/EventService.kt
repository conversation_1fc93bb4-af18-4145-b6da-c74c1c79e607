package services.oneteam.ai.shared.domains.event

import kotlinx.coroutines.currentCoroutineContext
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.middlewares.RequestContext
import java.time.Instant
import java.util.*

class EventService(
    private val eventDispatcher: EventDispatcher,
) {
    suspend fun create(event: Event.ForCreate): Event {
        val tenant = currentCoroutineContext()[RequestContext]!!.tenant

        val forJsonEvent = Event.ForJson(
            workspaceId = event.workspaceId,
            eventProperties = event.eventProperties,
            id = Event.Id(UUID.randomUUID().toString()),
            tenantId = tenant.id,
            status = EventStatus.QUEUED,
            entityMetadata = EntityMetadata(Instant.now(), Instant.now()),
            eventGroupId = event.eventGroupId,
        )

        eventDispatcher.enqueueEvent(forJsonEvent, currentCoroutineContext())

        return forJsonEvent
    }
}